# LogControl 接口详细日志调试

## 🎯 目标

为 `/api/logcontrol/device/upload` 和 `/api/logcontrol/config/get` 两个接口添加详细的请求参数和请求头日志输出，以便调试接口问题。

## 🔧 修改内容

### 1. 添加详细日志方法

在 `TokenInterceptor.kt` 中新增 `logLogControlRequestDetails()` 方法：

```kotlin
/**
 * 详细记录LogControl接口的请求信息
 */
private fun logLogControlRequestDetails(request: Request, url: String) {
    try {
        Log.i(TAG, "========== LogControl接口详细信息 ==========")
        Log.i(TAG, "URL: $url")
        Log.i(TAG, "Method: ${request.method}")
        
        // 输出所有请求头（包括认证头的完整信息）
        Log.i(TAG, "请求头详情:")
        request.headers.forEach { header ->
            when {
                header.first.equals("x-auth-token", true) -> {
                    Log.i(TAG, "  ${header.first}: ${header.second}")
                }
                header.first.equals("X-Auth-Token", true) -> {
                    Log.i(TAG, "  ${header.first}: ${header.second}")
                }
                else -> {
                    Log.i(TAG, "  ${header.first}: ${header.second}")
                }
            }
        }
        
        // 输出请求体（如果有）
        val requestBody = request.body
        if (requestBody != null) {
            Log.i(TAG, "请求体信息:")
            Log.i(TAG, "  Content-Type: ${requestBody.contentType()}")
            Log.i(TAG, "  Content-Length: ${requestBody.contentLength()}")
            
            // 尝试读取请求体内容（仅对小于1KB的内容）
            if (requestBody.contentLength() < 1024) {
                try {
                    val buffer = okio.Buffer()
                    requestBody.writeTo(buffer)
                    val bodyContent = buffer.readUtf8()
                    Log.i(TAG, "  请求体内容: $bodyContent")
                } catch (e: Exception) {
                    Log.w(TAG, "  无法读取请求体内容: ${e.message}")
                }
            } else {
                Log.i(TAG, "  请求体过大，跳过内容输出")
            }
        } else {
            Log.i(TAG, "无请求体")
        }
        
        // 输出URL参数
        val httpUrl = request.url
        if (httpUrl.querySize > 0) {
            Log.i(TAG, "URL参数:")
            for (i in 0 until httpUrl.querySize) {
                Log.i(TAG, "  ${httpUrl.queryParameterName(i)}: ${httpUrl.queryParameterValue(i)}")
            }
        } else {
            Log.i(TAG, "无URL参数")
        }
        
        Log.i(TAG, "==========================================")
    } catch (e: Exception) {
        Log.e(TAG, "记录LogControl请求详情时出错: ${e.message}")
    }
}
```

### 2. 调用详细日志方法

在请求处理逻辑中添加调用：

```kotlin
// 详细日志输出LogControl接口的请求信息
val isLogControlApi = original.url.toString().contains("/api/logcontrol/")
if (isLogControlApi) {
    logLogControlRequestDetails(request, original.url.toString())
}
```

### 3. 添加必要的导入

```kotlin
import okio
```

## 📋 日志输出内容

对于LogControl接口，将输出以下详细信息：

1. **基本信息**：
   - 完整URL
   - HTTP方法（GET/POST等）

2. **请求头详情**：
   - 所有请求头的完整信息
   - 包括认证头的实际值（不隐藏）
   - x-auth-token 和 X-Auth-Token 都会完整显示

3. **请求体信息**：
   - Content-Type
   - Content-Length
   - 请求体内容（小于1KB时）

4. **URL参数**：
   - 所有查询参数的键值对

## 🎯 预期效果

运行应用后，在日志中可以看到类似以下格式的详细信息：

```
========== LogControl接口详细信息 ==========
URL: https://plat.sczjzy.com.cn/api/logcontrol/config/get
Method: GET
请求头详情:
  x-auth-token: 922c8780-f1a2-4c3f-b11b-6e089687089a
  X-User-Id: 12345
  X-Device-Id: device123
  X-App-Version: 1.0.0
请求体信息:
  Content-Type: application/json
  Content-Length: 256
  请求体内容: {"key":"value"}
URL参数:
  param1: value1
  param2: value2
==========================================
```

## 📝 调试建议

1. **清除应用数据**，重新登录
2. **观察日志**，查找 "LogControl接口详细信息" 标记
3. **检查以下内容**：
   - 认证头格式是否正确（x-auth-token vs X-Auth-Token）
   - 认证头值是否存在且正确
   - 请求参数是否完整
   - URL是否正确

## ✅ 调试完成

此修改将提供LogControl接口的完整请求信息，帮助定位问题根源。
