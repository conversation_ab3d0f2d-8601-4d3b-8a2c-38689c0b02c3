<template>
	<view class="main">
		<view class="address">
			<view class="address-info">
				<view class="address-title">
					{{ details.customer.name }}
				</view>
				<view class="address-details" @click="openMap">
					{{ details.customer.address }}
				</view>
			</view>
			<view class="btns">
				<view class="btn" v-if="details.status.value !== 'to_be_settled' && details.status.value !== 'completed'" @click="toStore">耗材仓库</view>
				<view class="transfer" v-if="details.status.value !== 'completed'" @click="orderTransfer">工单转让</view>
			</view>
		</view>

		<view class="progress" v-if="details.status && details.status.value !== 'close'">
			<u-collapse :border="false" :value="['0']">
				<u-collapse-item title="维修进度跟踪" name="0">
					<view class="progress-details">
						<u-steps :current="getStepByStatus(details.currentProcess)" direction="column" activeColor="#FF541E" inactiveColor="#D9D9D9" dot>
							<u-steps-item :title="item.label" v-for="item in processList" :key="item.value" :desc="details[item.desc]"></u-steps-item>
						</u-steps>
					</view>
				</u-collapse-item>
			</u-collapse>
		</view>
		<view class="des">
			<view class="des-class">
				<view class="left-item">
					<image
						class="des-image"
						:src="
							details.customerDeviceGroup && details.customerDeviceGroup.deviceGroupImg && details.customerDeviceGroup.deviceGroupImg.url
								? details.customerDeviceGroup.deviceGroupImg.url
								: require('../../static/images/top.png')
						"
						mode=""
					></image>
					<view class="des-class-details">
						<view class="model-number">
							{{ details.customerDeviceGroup.deviceGroup.label }}
						</view>
						<view class="model-brand">
							{{ details.brand + details.machine }}
						</view>
						<view class="model-brand">服务类型：{{ details.serType && details.serType.label }}</view>
					</view>
				</view>
				<view class="right-item">
					<view class="btn" @click="historyRecord">历史维修记录</view>
					<view class="btn" @click="replacementRecord(details)">PM件更换记录</view>
				</view>
			</view>
			<view class="des-number des-boder">
				黑白计数器：
				<view class="count">{{ details.blackWhiteCount }}</view>
			</view>
			<!-- 	<view class="des-number des-boder" v-if="details.treatyType.value === '1230' || details.treatyType.value === '1202'">
				黑白废张数：
				<view class="count">{{ details.blackWhiteExclude ? details.blackWhiteExclude : 0 }}</view>
			</view> -->
			<view class="des-number des-boder">
				彩色计数器：
				<view class="count">{{ details.colorCount }}</view>
			</view>
			<view class="des-number des-boder">
				五色计数器：
				<view class="count">{{ details.fiveColourCount }}</view>
			</view>
			<!-- 	<view class="des-number des-boder" v-if="details.treatyType.value === '1230' || details.treatyType.value === '1202'">
				彩色废张数：
				<view class="count">{{ details.colorExclude ? details.colorExclude : 0 }}</view>
			</view> -->
			<view class="des-number des-boder">
				上次维修后到目前的印量：
				<view class="count">{{ details.printCount }}</view>
			</view>
			<view class="des-number des-boder">
				故障代码/卡纸代码：
				<view class="count">{{ details.errorCode }}</view>
			</view>
			<!-- <view class="look" @click="toKnowledgeBase">
        去知识库<u-icon name="arrow-right" color="#FF541E"></u-icon>
      </view> -->
			<!-- <view class="toast des-boder">
        (安装本印猫客户端，故障信息第一时间推送到您微信)
      </view> -->

			<view class="dex-title">故障描述:</view>
			<view class="des-text des-boder" style="word-break: break-all">
				{{ details.excDesc }}
			</view>
			<view class="dex-title">故障照片:</view>
			<view class="des-image-list des-boder">
				<image v-for="(item, index) in details.excPics" :key="item.key" :src="item.url" @click="previewImage(details.excPics, index)" mode=""></image>
			</view>
			<view class="name des-boder">
				<view class="name-top">
					<view class="name-left">
						报修客户：
						<view class="staust">
							<text>{{ details.customerStaff.name }}</text>
						</view>
					</view>
					<view class="name-right">
						<view class="phone" @click="callToCustomer">
							联系客户
							<u-icon name="phone-fill" size="22" color="#FF541E"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<view class="des-number" v-if="details.expectArriveTime">
				期望工程师上门时间：
				<view class="count">{{ details.expectArriveTime }}</view>
			</view>
			<view class="des-number" v-if="details.prospectArriveTime">
				工程师预计上门时间：
				<view class="count">{{ details.prospectArriveTime }}</view>
			</view>

			<view class="des-number des-boder">
				工单号：
				<view class="count">{{ details.code }}</view>
			</view>
			<!-- <view class="des-number">
        报修发起时间：<view class="count">{{ details.createdAt }}</view>
      </view>
      <view class="des-number" v-if="details.actualArriveTime">
        到店时间：<view class="count">{{ details.actualArriveTime }}</view>
      </view> -->
			<view class="des-number" v-if="details.travelTime">
				路途时长：
				<view class="count">{{ details.travelTime }}</view>
			</view>
			<view class="des-number" v-if="details.fixTime">
				维修时长：
				<view class="count">{{ details.fixTime }}</view>
			</view>
		</view>

		<view class="price">
			<view class="des-number des-boder">
				上门费：
				<view class="count">{{ details.visitPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.longWayVisitPay">
				远程上门费：
				<view class="count">{{ details.longWayVisitPay }}元</view>
			</view>

			<view class="des-number des-boder" v-if="details.repairPay">
				维修诊断费：
				<view class="count">{{ details.repairPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.actualReplacePay">
				零件更换费：
				<view class="count">{{ details.actualReplacePay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.itemPay">
				维修耗材费用：
				<view class="count">{{ details.itemPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.engineerAdditionalPay">
				工程师追加费用：
				<view class="count">{{ details.engineerAdditionalPay }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.derateAmount">
				工程师减免费用：
				<view class="count">-{{ details.derateAmount }}元</view>
			</view>
			<view class="des-number des-boder">
				会员减免：
				<view class="count">-{{ details.discountAmount }}元</view>
			</view>
			<view class="des-number des-boder" v-if="details.additionalPay">
				客户追加报酬：
				<view class="count">{{ details.additionalPay }}元</view>
			</view>
			<view v-if="!details.isContracted" class="des-number des-boder">
				工程师追加费用：
				<view class="count">
					<input
						v-if="details.status.value === 'engineer_arrive'"
						type="text"
						v-model="details.engineerAdditionalPay"
						@input="handleChangeAddInput"
						placeholder="请输入追加费用"
						value=""
					/>
					<text v-else>-{{ details.engineerAdditionalPay ? details.engineerAdditionalPay : '0.00' }}</text>
					元
				</view>
			</view>
			<view v-if="!details.isContracted" class="des-number des-boder">
				工程师减免费用：
				<view class="count">
					<input v-if="details.status.value === 'engineer_arrive'" type="text" v-model="derateAmount" @input="handleChangeInput" placeholder="请输入减免费用" value="" />
					<text v-else>-{{ details.derateAmount ? details.derateAmount : '0.00' }}</text>
					元
				</view>
			</view>

			<view class="des-number des-boder">
				应付维修费用：
				<view class="count">
					合计
					<text style="color: #ff541e; font-weight: bold">¥{{ details.totalAmount }}</text>
					元
				</view>
			</view>
			<view class="des-number des-boder" v-if="details.isContracted" style="margin-bottom: 40rpx">
				实付维修费用：
				<view class="count">
					合计
					<text style="color: #ff541e; font-weight: bold">¥ {{ details.totalPay }}</text>
					元
				</view>
			</view>
		</view>
		<view class="footer">
			<!-- <button class="confirm-btn" @click="createOrder(false, 'add')">新建发货单</button> -->
			<!-- 工程师工程师接单状态 -->
			<view v-if="details.status.value === 'engineer_receive'" class="confirm-btn" @click="handleBtnClick('开始出发')">开始出发</view>

			<!-- 工程师出发状态 -->
			<view v-if="details.status.value === 'engineer_departure'" class="confirm-btn" @click="handleBtnClick('确认到店')">确认到店</view>
			<!-- 工程师到店维修状态 -->
			<view v-if="details.status.value === 'engineer_arrive'" class="confirm-btn" @click="handleBtnClick('填写维修报告')">填写维修报告</view>
			<!-- 客户没确认维修报告支持主动撤回 -->
			<view v-if="details.status.value === 'wait_confirmed_report'" class="confirm-btn" @click="handleBtnClick('撤回维修报告')">撤回维修报告</view>
			<!-- 状态：待确认维修报告、待结算、已完成 -->

			<view
				v-if="details.status.value === 'wait_confirmed_report' || details.status.value === 'to_be_settled' || details.status.value === 'completed'"
				class="confirm-btn"
				@click="handleBtnClick('查看维修报告')"
			>
				查看维修报告
			</view>
		</view>
		<!-- 选择维修工程师 -->
		<uni-popup ref="transferPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">选择转让工程师</text>
					<text class="popup-close" @click="closeTransferPopup">×</text>
				</view>
				<view class="engineer-list">
					<view v-for="(engineer, index) in workerList" :key="index" class="engineer-item" @click="selectTransferEngineer(engineer)">
						<text>{{ engineer.name }}</text>
						<text>{{ (engineer.skillExp && engineer.skillExp.label) || '' }}</text>
					</view>
				</view>
			</view>
		</uni-popup>
		<rfLoading isFullScreen :active="isLoading"></rfLoading>
	</view>
</template>

<script>
import { logout } from '../../api/login';
import {
	cancelWorkOrder,
	getWorkOrderDetail,
	cancelAcceptCancelOrder,
	goWorkOrder,
	arriveWorkOrder,
	submitReport,
	getIgnoreLocation,
	discountWorkOrder,
	addWorkOrderFee,
	engineerList,
	assignEngineer,
	recallWorkOrder
} from '@/api/workOrder';
import { debounce } from '@/utils/index.js';
const userInfo = uni.getStorageSync('userInfo');
export default {
	components: {},
	data() {
		return {
			paddingTop: '',
			price: 300,
			disabled: true,
			isLoading: true,
			detailId: null,
			details: {},
			userInfo: userInfo,
			islookRule: false,
			derateAmount: '', // 工程师减免费用
			additionalAmount: '', // 工程师追加费用
			workerList: [], // 工程师列表
			engineerId: '',
			engineerName: '',
			processList: [
				{ value: 'CREATE', label: '发起报修', desc: 'createdAt' },
				{
					value: 'ENGINEER_RECEIVE',
					label: '工程师接单',
					desc: 'orderReceiveTime'
				},
				{
					value: 'ENGINEER_DEPARTURE',
					label: '工程师出发',
					desc: 'departureTime'
				},
				{
					value: 'ENGINEER_ARRIVE',
					label: '到店维修',
					desc: 'actualArriveTime'
				},
				{
					value: 'WAIT_CONFIRM',
					label: '确认维修报告',
					desc: 'sendReportTime'
				},
				{ value: 'DONE', label: '已完成', desc: 'completedAt' }
			]
		};
	},
	onShow() {
		if (this.detailId) {
			this.loadData();
		}
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.loadData();
		uni.stopPullDownRefresh();
	},
	created() {},
	onLoad: function ({ id }) {
		console.log(id);
		if (!id) {
			uni.navigateTo({
				url: `/pages/workOrder/myWorkOrder`
			});
			return;
		}
		this.detailId = id;
		this.loadData();
	},
	methods: {
		onLongTap() {
			const { customerRegion, customer } = this.details;
			const { address } = customer;
			uni.setClipboardData({ data: `${customerRegion}${address}` });
		},
		openMap() {
			const {
				customer: { address, location }
			} = this.details;
			wx.getLocation({
				type: 'gcj02',
				success: function (res) {
					const latitude = parseFloat(location.latitude) || res.latitude;
					const longitude = parseFloat(location.longitude) || res.longitude;
					wx.openLocation({
						latitude,
						longitude,
						name: address,
						scale: 18,
						success: function (result) {
							console.log(result);
						},
						fail: function (error) {
							uni.showToast({
								title: '打开地图失败',
								icon: 'none'
							});
						}
					});
				},
				fail: function (err) {
					uni.showToast({
						title: '定位失败',
						icon: 'none'
					});
				}
			});
		},

		/**
		 *@description 工单转让
		 */
		orderTransfer() {
			engineerList(this.details.productId)
				.then((res) => {
					this.workerList = res.data;
					// this.islookRule = true;
					this.$refs.transferPopup.open();
				})
				.catch((err) => {
					uni.showToast({
						title: err || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				});
		},
		closeTransferPopup() {
			this.$refs.transferPopup.close();
		},
		selectTransferEngineer(engineer) {
			uni.showModal({
				title: '提示',
				content: `确定将工单转让给${engineer.name}吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await assignEngineer({
								id: this.details.id,
								engineerId: engineer.id
							});
							if (result.code === 200) {
								uni.showToast({
									title: '转让成功',
									icon: 'success'
								});
								this.closeTransferPopup();
								// 重定向到工单列表
								setTimeout(() => {
									uni.redirectTo({
										url: '/pages/workOrder/myWorkOrder'
									});
								}, 300);
							}
						} catch (error) {
							uni.showToast({
								title: error || '转让失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		/**
		 * @@description 确认工单转让
		 */
		confrimTransfer() {
			const that = this;
			if (!that.engineerId) {
				uni.showToast({
					title: '请选择转让工程师',
					icon: 'none'
				});
				return;
			}
			uni.showModal({
				title: '提示',
				content: `确定将工单转让给 ${that.engineerName} 吗？`,
				success: function (res) {
					if (res.confirm) {
						assignEngineer({
							id: that.details.id,
							engineerId: that.engineerId
						})
							.then((res) => {
								setTimeout(() => {
									that.engineerId = '';
									that.engineerName = '';
									that.islookRule = false;
									uni.redirectTo({
										url: '/pages/workOrder/myWorkOrder'
									});
								}, 300);
							})
							.catch((err) => {
								uni.showToast({
									title: err || '系统出错啦，请稍后再试！',
									icon: 'none'
								});
							});
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		handleChangeInput: debounce(function (e) {
			this.derateAmount = e.detail.value;
			this.handleDerateAmount();
		}, 500),
		/**
		 * @description 工程师减免费用
		 */
		handleDerateAmount() {
			const { detailId, derateAmount } = this;
			const params = {
				id: detailId,
				derateAmount: Number(derateAmount)
			};
			discountWorkOrder(params)
				.then((res) => {
					uni.showToast({
						title: '修改成功',
						icon: 'none'
					});
					this.loadData();
				})
				.catch((err) => {
					uni.showToast({
						title: err || err.message || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				});
		},
		// 追加费用
		handleChangeAddInput: debounce(function (e) {
			this.additionalAmount = e.detail.value;
			this.handleAddFee();
		}, 500),
		// 确认追加费用
		handleAddFee() {
			const { detailId, additionalAmount } = this;
			const params = {
				id: detailId,
				engineerAdditionalPay: Number(additionalAmount)
			};
			discountWorkOrder(params)
				.then((res) => {
					uni.showToast({
						title: '修改成功',
						icon: 'none'
					});
					this.loadData();
				})
				.catch((err) => {
					uni.showToast({
						title: err || err.message || '系统出错啦，请稍后再试！',
						icon: 'none'
					});
				});
		},
		handleSave() {
			this.handleDerateAmount();
		},
		async loadData() {
			this.isLoading = true;
			try {
				const result = await getWorkOrderDetail(this.detailId);
				console.log(result);
				if (result.code === 200) {
					this.details = result.data;
					uni.setStorageSync('orderDetails', this.details);
				}
			} catch (err) {
				console.error(err);
			} finally {
				this.isLoading = false;
			}
		},
		getStepByStatus(status) {
			return this.processList.findIndex((item) => item.value === status);
		},

		// 历史维修记录
		historyRecord() {
			console.log('历史维修记录');
			console.log(this.details.deviceGroupId);
			let id = this.details.deviceGroupId;
			uni.navigateTo({
				url: `/pages/workOrder/historyRecord?id=${id}`
			});
		},
		// PM件更换记录
		replacementRecord(item) {
			let id = item.customerDeviceGroup.id;
			let productInfo = item.brand + '/' + item.machine;
			let deviceGroup = item.customerDeviceGroup.deviceGroup.label;
			uni.navigateTo({
				url: `/pages/workOrder/pm?productInfo=${productInfo}&deviceGroup= ${deviceGroup}&id=${id}`
			});
		},
		// 联系工程师
		callToCustomer() {
			uni.makePhoneCall({
				phoneNumber: this.details.customerStaff.tel,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},

		// 开始出发
		async startGo() {
			console.log('开始出发');
			const _this = this;
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					goWorkOrder({
						id: _this.detailId,
						location: {
							longitude: res.longitude,
							latitude: res.latitude,
							system: 'GCJ_02'
						}
					})
						.then((res) => {
							console.log(res);
						})
						.catch((err) => {
							console.error(err);
						})
						.finally(() => {
							_this.loadData();
						});
				},
				fail: function (err) {
					console.log(err, '4++++++++++');
					uni.showToast({
						title: '获取地理位置失败',
						icon: 'none'
					});
					const userId = uni.getStorageSync('userInfo').id;
					getIgnoreLocation().then((res) => {
						console.log(res, '44444444444');
						if (res.code === 200 && res.data.includes(userId)) {
							goWorkOrder({
								id: _this.detailId,
								location: {
									longitude: '',
									latitude: '',
									system: 'GCJ_02'
								}
							})
								.then((res) => {
									console.log(res);
								})
								.catch((err) => {
									console.error(err);
								})
								.finally(() => {
									_this.loadData();
								});
						}
					});
					// 根据具体错误信息进行处理
				}
			});
		},
		// 工程师 取消工单
		handleCancelWorkOrder() {
			uni.showModal({
				title: '提示',
				content: '取消工单后，将无法恢复哟。\n（接单后，您取消工单需要店铺确认后才可生效。）',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							const result = await cancelWorkOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								uni.showToast({
									title: '取消成功',
									icon: 'none',
									duration: 2000,
									success: () => {
										setTimeout(() => {
											uni.$emit('refreshWorkOrderList');
											uni.navigateBack();
										}, 2000);
									}
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
						}
					}
				}
			});
		},
		// 工程师 确认到店
		handleConfirmArrival() {
			console.log('工程师 确认到店');
			const _this = this;
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					arriveWorkOrder({
						id: _this.detailId,
						location: {
							longitude: res.longitude,
							latitude: res.latitude,
							system: 'GCJ_02'
						}
					})
						.then((res) => {
							console.log(res);
						})
						.catch((err) => {
							console.error(err);
						})
						.finally(() => {
							_this.loadData();
						});
				},
				fail: function (err) {
					uni.showToast({
						title: '获取地理位置失败',
						icon: 'none'
					});
					const userId = uni.getStorageSync('userInfo').id;
					getIgnoreLocation().then((res) => {
						console.log(res, '44444444444');
						if (res.code === 200 && res.data.includes(userId)) {
							arriveWorkOrder({
								id: _this.detailId,
								location: {
									longitude: res.longitude,
									latitude: res.latitude,
									system: 'GCJ_02'
								}
							})
								.then((res) => {
									console.log(res);
								})
								.catch((err) => {
									console.error(err);
								})
								.finally(() => {
									_this.loadData();
								});
						}
					});
					// 根据具体错误信息进行处理
				}
			});
		},
		// 工程师 同意取消
		handleAgreeCancel() {
			uni.showModal({
				title: '提示',
				content: '同意取消工单后，该工单将关闭，无法恢复哟。',
				success: async (res) => {
					if (res.confirm) {
						try {
							this.isLoading = true;
							console.log('同意取消');
							const result = await cancelAcceptCancelOrder(this.detailId);
							console.log(result);
							if (result.code === 200) {
								console.log('取消成功');
								uni.showToast({
									title: '取消成功',
									icon: 'none'
									// duration: 2000,
									// success: () => {
									//   setTimeout(() => {
									//     uni.navigateBack();
									//   }, 2000);
									// },
								});
							} else if (result.code === 406) {
								uni.showToast({
									title: '取消失败',
									icon: 'none'
								});
								return;
							}
						} catch (err) {
							console.error(err);
						} finally {
							this.isLoading = false;
							this.loadData();
						}
					}
				}
			});
		},
		// 填写维修报告
		handleFillInRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/repairReport?type=0`
			});
		},
		// 撤回维修报告
		handleWithdrawReport() {
			uni.showModal({
				title: '提示',
				content: '确定要撤回维修报告吗？',
				success: (res) => {
					if (res.confirm) {
						recallWorkOrder(this.detailId).then((res) => {
							this.loadData();
						});
					}
				}
			});
		},
		// 查看维修报告
		handleCheckRepairReport() {
			uni.navigateTo({
				url: `/pages/workOrder/reportDetails?id=` + this.detailId
			});
		},

		handleBtnClick(type) {
			console.log(type);
			switch (type) {
				case '开始出发':
					this.startGo();
					break;
				case '取消工单':
					this.handleCancelWorkOrder();
					break;
				case '同意取消':
					this.handleAgreeCancel();
					break;
				case '确认到店':
					this.handleConfirmArrival();
					break;
				case '填写维修报告':
					this.handleFillInRepairReport();
					break;
				case '撤回维修报告':
					this.handleWithdrawReport();
					break;
				case '查看维修报告':
					this.handleCheckRepairReport();
					break;
				default:
					break;
			}
		},
		previewImage(url, index) {
			uni.previewImage({
				current: index,
				urls: url.map((item) => item.url)
			});
		},
		toKnowledgeBase() {
			uni.navigateTo({
				url: '/pages/learn/index'
			});
		},
		toStore() {
			console.log(this.details);
			const customerId = this.details.customer.id;
			const deviceGroupId = this.details.deviceGroupId;
			uni.navigateTo({
				url: `/pages/workOrder/customStore?customerId=${customerId}&deviceGroupId=${deviceGroupId}`
			});
		}
	}
};
</script>

<style scoped lang="scss">
.main {
	width: 100%;
	height: 100%;
	background-color: #f5f6f8;
	padding-bottom: 40rpx;
	.head-nav {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10000;
	}

	.address {
		width: 100%;
		min-height: 130rpx;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx 24rpx;
		box-sizing: border-box;
		position: relative;
		display: flex;
		justify-content: space-between;
		.address-info {
			width: 70%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.address-title {
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #0c0c0c;
				line-height: 39rpx;
			}

			.address-details {
				margin-top: 12rpx;
				font-size: 27rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 39rpx;
			}
		}
		.btns {
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: center;
			.btn {
				text-align: center;
				width: 80%;
				border: 1px solid #f2f2f2;
				border-radius: 10rpx;
				padding: 8rpx 12rpx;
				margin-bottom: 10rpx;
			}
			.transfer {
				width: 60%;
				height: 45rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: none !important;
				color: #fff;
				border-radius: 37px;
				font-size: 25rpx;
				margin: 0;
				background: linear-gradient(90deg, #f5c744 0%, #ee822f 100%);
			}
		}
	}

	.progress {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx 24rpx 0rpx;
		box-sizing: border-box;

		.progress-title {
			font-size: 27rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #0c0c0c;
			line-height: 39rpx;
		}

		.progress-details {
			width: 100%;
			min-height: 240rpx;
			margin-top: 12rpx;

			/deep/.u-steps {
				height: 200px;
				margin-top: 8rpx;

				.u-steps-item {
					height: 40rpx !important;

					.u-text__value--content {
						font-size: 28rpx !important;
					}

					.u-text__value--main {
						color: #ff541e;
						font-size: 32rpx !important;
						line-height: normal;
					}
				}
			}
		}
	}

	.des {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 24rpx;
		box-sizing: border-box;

		.des-class {
			width: 100%;
			padding-bottom: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left-item {
				width: 70%;
				display: flex;
				align-items: center;
			}
			.right-item {
				width: 30%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.btn {
					text-align: center;
					width: 100%;
					border: 1px solid #f2f2f2;
					border-radius: 10rpx;
					padding: 8rpx 12rpx;
					margin-bottom: 10rpx;
				}
			}
			.des-image {
				width: 154rpx;
				height: 154rpx;
				border-radius: 13rpx;
				margin-right: 16rpx;
			}

			.des-class-details {
				flex: 1;
				height: 154rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				font-size: 27rpx;
				font-family: PingFang SC;

				.model-number {
					width: 100%;
					font-weight: bold;
					color: #0c0c0c;
					line-height: 40rpx;
				}

				.model-brand {
					width: 100%;
					margin-top: 10rpx;
					font-weight: 500;
					color: #535353;
					line-height: 40rpx;
				}
			}
		}

		.des-number {
			width: 100%;
			min-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;

			.count {
				color: #666666;
			}
		}

		.look {
			margin-top: 10rpx;
			font-size: 27rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ff541e;
			text-align: right;
		}

		.toast {
			margin-top: 20rpx;
			padding-bottom: 20rpx;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
		}

		.dex-title {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #323333;
			margin-top: 20rpx;
		}

		.des-text {
			min-height: 127rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #666666;
		}

		.des-image-list {
			width: 100%;
			height: 206rpx;
			display: flex;
			align-items: center;

			image {
				width: 154rpx;
				height: 154rpx;
				//background: #474747;
				border-radius: 13rpx;
				margin-right: 20rpx;
			}
		}

		.name {
			width: 100%;
			padding: 26rpx 0;

			.name-top {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;

				.name-left {
					color: #323333;
					display: flex;
					align-items: center;

					.staust {
					}
				}

				.name-right {
					color: #666666;
				}
			}

			.phone {
				width: 100%;
				height: 44rpx;
				margin-top: 22rpx;

				/deep/.u-icon {
					float: right;
				}
			}
		}
	}

	.time,
	.price {
		width: 100%;
		margin-top: 22rpx;
		background-color: #fff;
		padding: 0 24rpx;
		box-sizing: border-box;
	}
	.price {
		margin-bottom: 120rpx;
	}
	.des-number {
		width: 100%;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #323333;

		.count {
			display: flex;
			align-items: center;
			color: #666666;

			input {
				min-width: 200rpx;
				text-align: right;
				margin-right: 4rpx;
				border: 2rpx solid #f6f6f6;
				width: 200rpx;
			}
		}

		.des-pay {
			width: 100%;
			padding: 16rpx 0;
			display: flex;
			align-items: center;
			flex-direction: column;

			.add-pay {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.price-change {
				width: 100%;
				height: 40rpx;
				font-size: 27rpx;
				color: #ff541e;
				text-align: right;
			}
		}
	}

	.des-boder {
		border-bottom: 1rpx solid #f3f3f3;
	}
}
.footer {
	width: 100%;
	padding: 18rpx 20rpx 42rpx 20rpx;
	display: flex;
	justify-content: center;
	gap: 20rpx;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;

	.confirm-btn {
		width: 80%;
		margin: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(90deg, #e5452f 0%, #ee822f 100%);
		border-radius: 50px;
		color: #fff;
	}
}
::v-deep .u-collapse-item__content__text {
	padding: 0 !important;
}
::v-deep .u-cell__body {
	padding: 20rpx 0 !important;
	font-weight: bold;
}
.popup-content {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 24rpx 24rpx 0 0;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
		}

		.popup-close {
			font-size: 40rpx;
			color: #666;
			padding: 10rpx;
		}
	}
}

.engineer-list {
	max-height: 50vh;
	overflow-y: auto;

	.engineer-item {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 0;
		border-bottom: 1px solid #eee;

		&:active {
			background-color: #f5f5f5;
		}
	}
}
</style>
