2025-08-28 19:36:50.379 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=5532.36ms min=4.27ms max=38648.34ms count=7
2025-08-28 19:36:50.459 16338-16338 TokenInterceptor        com.example.repairorderapp           I  用户选择立即重新登录 (第1次)
2025-08-28 19:36:50.459 16338-16338 TokenInterceptor        com.example.repairorderapp           I  开始清除Token并跳转登录页面
2025-08-28 19:36:50.459 16338-16338 TokenInterceptor        com.example.repairorderapp           D  Token过期状态已重置
2025-08-28 19:36:50.484 16338-16338 TokenInterceptor        com.example.repairorderapp           I  Token清除完成，已跳转到登录页面
2025-08-28 19:36:50.485 16338-16338 WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@ae3b344
2025-08-28 19:36:50.567 16338-16396 HWUI                    com.example.repairorderapp           D  endAllActiveAnimators on 0x71405dad2f20 (RippleDrawable) with handle 0x7140ede256d0
2025-08-28 19:36:50.590 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity暂停: MainActivity
2025-08-28 19:36:50.609 16338-16338 TokenInterceptor        com.example.repairorderapp           D  Token过期对话框被关闭
2025-08-28 19:36:50.635 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity创建: LoginActivity
2025-08-28 19:36:50.637 16338-16338 RepairOrderApp          com.example.repairorderapp           D  为 LoginActivity 设置全局触摸监听
2025-08-28 19:36:50.665 16338-16805 ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn
2025-08-28 19:36:50.669 16338-16338 LoginActivity           com.example.repairorderapp           D  开始获取验证码...
2025-08-28 19:36:50.670 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getCaptcha
2025-08-28 19:36:50.673 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:50.673 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-08-28 19:36:50.677 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-08-28 19:36:50.677 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:50.677 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:50.678 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:36:50.678 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-08-28 19:36:50.678 16338-16792 TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-08-28 19:36:50.678 16338-16792 TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-08-28 19:36:50.679 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-08-28 19:36:50.679 16338-16792 TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-08-28 19:36:50.679 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:36:50.679 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:36:50.679 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:36:50.680 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity开始: LoginActivity
2025-08-28 19:36:50.684 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity恢复: LoginActivity
2025-08-28 19:36:50.732 16338-16476 TrafficStats            com.example.repairorderapp           D  tagSocket(134) with statsTag=0xffffffff, statsUid=-1
2025-08-28 19:36:50.785 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/captcha (107ms)
2025-08-28 19:36:50.785 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:36:50.786 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:36:52 GMT
2025-08-28 19:36:50.789 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:36:50.790 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:36:50.790 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:36:50.811 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"ddb3ccb2-771f-4b4d-9fca-ec281966647b","second":"data:image/gif;base64,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
2025-08-28 19:36:50.811 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-08-28 19:36:50.811 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-08-28 19:36:50.811 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  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"}}
2025-08-28 19:36:50.811 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (15161-byte body)
2025-08-28 19:36:50.823 16338-16805 TrafficStats            com.example.repairorderapp           D  tagSocket(130) with statsTag=0xffffffff, statsUid=-1
2025-08-28 19:36:50.891 16338-16338 AssistStructure         com.example.repairorderapp           I  Flattened final assist data: 3776 bytes, containing 1 windows, 17 views
2025-08-28 19:36:50.894 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码原始响应长度: 15161
2025-08-28 19:36:50.898 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码token: ddb3ccb2-771f-4b4d-9fca-ec281966647b
2025-08-28 19:36:50.901 16338-16338 LoginActivity           com.example.repairorderapp           D  Base64字符串前20个字符: R0lGODlhggAwAPcAAAAA
2025-08-28 19:36:50.901 16338-16338 LoginActivity           com.example.repairorderapp           D  完整Base64长度: 15044
2025-08-28 19:36:50.901 16338-16338 LoginActivity           com.example.repairorderapp           D  解码后字节数: 11282
2025-08-28 19:36:50.901 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码图片解码成功: 130x48
2025-08-28 19:36:51.087 16338-16338 ImeBackDispatcher       com.example.repairorderapp           E  Ime callback not found. Ignoring unregisterReceivedCallback. callbackId: 44693925
2025-08-28 19:36:51.161 16338-16338 RemoteInpu...ectionImpl com.example.repairorderapp           W  requestCursorUpdates on inactive InputConnection
2025-08-28 19:36:51.406 16338-16805 ServerTest              com.example.repairorderapp           D  服务器连接测试1结果: 200
2025-08-28 19:36:51.406 16338-16805 ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:51.407 16338-16805 TrafficStats            com.example.repairorderapp           D  tagSocket(162) with statsTag=0xffffffff, statsUid=-1
2025-08-28 19:36:51.421 16338-16338 VRI[MainActivity]       com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-08-28 19:36:51.490 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity停止: MainActivity
2025-08-28 19:36:51.497 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity销毁: MainActivity
2025-08-28 19:36:51.501 16338-16338 PermissionManager       com.example.repairorderapp           D  移除权限观察者，当前观察者数量: 3
2025-08-28 19:36:51.503 16338-16338 WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@232e681
2025-08-28 19:36:51.723 16338-16805 ServerTest              com.example.repairorderapp           D  服务器连接测试2结果: 200
2025-08-28 19:36:52.698 16338-16477 TrafficStats            com.example.repairorderapp           D  tagSocket(134) with statsTag=0xffffffff, statsUid=-1
2025-08-28 19:36:52.742 16338-16338 LoginActivity           com.example.repairorderapp           D  开始获取验证码...
2025-08-28 19:36:52.742 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getCaptcha
2025-08-28 19:36:52.744 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:52.744 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-08-28 19:36:52.744 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-08-28 19:36:52.744 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-08-28 19:36:52.745 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:36:52.747 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:36:52.747 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:36:52.769 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=142.26ms min=5.64ms max=1550.69ms count=13
2025-08-28 19:36:52.834 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/captcha (89ms)
2025-08-28 19:36:52.834 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:36:52.835 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:36:54 GMT
2025-08-28 19:36:52.835 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:36:52.835 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:36:52.835 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:36:52.838 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"6864a3fd-b26f-4cde-9927-09611ff1b50e","second":"data:image/gif;base64,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
2025-08-28 19:36:52.838 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-08-28 19:36:52.838 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-08-28 19:36:52.838 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7///8I/gD/CRxIsKDBgwgTKlzIsKHDhxAjSpxIsaLFixgzatzIsaPFaB5DihwpMBoRkCRTqrQIqNjKlzAdCgMUs6bNgtB+oLzJE6YYZD0TNoMWVOMvmkUFSlsma5UZWkkxPhMiLag0ZrOcmjFzZlZUjF6S3Vw6a89WrrKU7fxasRcii1UhLm16dlUsZXHZYnSGBN3EZ03zKpxrduuqtII7rr1pZVlEZrD20FpsMJoyWWfqzlqW2GM0ObIow7wESdqtP3781Hp2UJqyVquSdSZoObJmZrNFQlslRxlPZk5uzSIa7dgfZrRrrXrlOCG0ZK8yb2VFi/XvO3uciVTGHaGTP0QH/gIWCK1sLO0In9VyJd3MqtUioy0T1uuWLVu4gi3zW1AaLTmwrLXLgAQWOKBD3Pl2ECR+9OeHM7FIRlQyFA50VVZnneEKMuGNdN+HINqiS4e0oSIHLfwZqOIuCHZ30DJ+7IQOMn6sYsxOFCZjWSztnRFLMiSSFOKQuIj2jzN64NHMPysa2KKCBqEzCzD/RIPMKn2gWFAxtXTS3iqb5TaSLsYwA41f6DxTzIcuJaSMHKvccqBFCSZ0yR9lwSILLXk1QwuWffThXnVi8pTMfbgsFE0sfsySy0V1HvQMJoCkJots0SQT4Vl9eFJLkHqh82FDtwTyR3MURRpNM8sog8yd/rUEwVoztUR31h6z2IJMjryKBZEwwArkTDLDCDNMRqLacktDA9ZSxx7WSVSnM9wVc8klxXB3iChabeUKob2GGxGwwkiTDLnGZqTMfW0uRKA0swBopEIJDjvMJbcgg4wwl1hyiGGXrhVur+MCey66wliUJjKIzksQgcj8gossqi35EHfJ/HJJJbREWMYfllwLCF4lyULGDGSAkmmFFqE7DDMOMzSkLb2AitCKtvzxxy3RLoQMLtd2694s2SoTSMJVllHGMtEw0wcZx7BcEbo2RzQkMFUf1OQutPghC3LOZXWtJX64Fwsy1lmmDDBIyVJGQX0UIjVF5EJp0cy2lNsQ/jDKOBOXNM4Ys0sus/Qxi2CWNSVdyLfUYnFBGBcBEhm8wQHHKs80Q8bcE5HbM0ZpJoPLfb/wN5EzvOxySx91KKOp0Ge8QgsyLh6UIEpn5EFcvMts7uvUwBZaUTS63Pf7RIdewkl7hs3STFyR2l47HMc/Y4fvF5E7UjP38RJRNM4gE8sqZ+j8hxl7nHHGKqBGb1D0cLxtIRxyH995sCIlu6yizBgjCyvM28MhKCGJVbACSLyx2z/cNxD1XesSBnQFHMhAuVjAwgxtoEUtarEr7iyDGc1wxjOgAY1oFEp7+RtVQcCXDFrAgnxn4cphkCHCOykCbAJpBh70gJ4F1q4//q5LxjOc0YxVmKIPY5jBGN6wClrQYhaykIUFYfEKV7BiFVhM3xkSgsKQMONDzUhGLWLxChjGMHayQEYzUPKMwkniFkgjCDr+gwqQMJAg0YOGHPzGjD2swlyck0gXI+IMWvQKGU6M4ivIMIYxkCGGWzkDK2JBC2U8IzHNiAwtcjEgVK0QFie640Dcx4w8wKEOfPpHjmK2kEFCpBZmoGAZIFkGRjZyDHxwRSxmkQwzCQYYyRiKa1yxClsUg0C+KJQz9sCIbLnphy+iENOAF8eILGMWrshmNl8xilF8whOb2EQtPHmzwV0CEIiwhYF68TmEDAMRl6iaKAnyjIHZjyGufbQa3hD1OIUA7Q+PkJOBsNaiXshBNPMcCDqCSLBf4Y8i0FCGMHxhH2X1QhjMMF1CIMQHWhSrFwPqRTBoCJHbISShCnXGMhr6kHy+ZBllrAUr9ULT/iRjFa0gWU13ypBo0GIPsMAhT4eKEGkcpp1ETSpBhKfUpjr1qVDlSUAAADs="}}
2025-08-28 19:36:52.839 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (13981-byte body)
2025-08-28 19:36:52.840 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码原始响应长度: 13981
2025-08-28 19:36:52.841 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码token: 6864a3fd-b26f-4cde-9927-09611ff1b50e
2025-08-28 19:36:52.841 16338-16338 LoginActivity           com.example.repairorderapp           D  Base64字符串前20个字符: R0lGODlhggAwAPcAAAAA
2025-08-28 19:36:52.841 16338-16338 LoginActivity           com.example.repairorderapp           D  完整Base64长度: 13864
2025-08-28 19:36:52.842 16338-16338 LoginActivity           com.example.repairorderapp           D  解码后字节数: 10397
2025-08-28 19:36:52.842 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码图片解码成功: 130x48
2025-08-28 19:36:53.713 16338-16338 ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:ad3e19f0: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-08-28 19:36:53.715 16338-16338 InputMethodManager      com.example.repairorderapp           D  showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{254351d VFED..CL. .F.P..ID 79,1189-665,1333 #7f090233 app:id/et_captcha aid=1073741826} flags=0 reason=SHOW_SOFT_INPUT
2025-08-28 19:36:53.974 16338-16338 InsetsController        com.example.repairorderapp           D  show(ime(), fromIme=true)
2025-08-28 19:36:54.179 16338-16338 ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:ad3e19f0: onShown
2025-08-28 19:36:54.230 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=208.09ms min=9.60ms max=884.24ms count=7
2025-08-28 19:36:54.698 16338-16476 TrafficStats            com.example.repairorderapp           D  tagSocket(156) with statsTag=0xffffffff, statsUid=-1
2025-08-28 19:36:55.245 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=507.39ms min=496.96ms max=517.82ms count=2
2025-08-28 19:36:56.462 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=405.36ms min=229.15ms max=497.55ms count=3
2025-08-28 19:36:57.762 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=323.78ms min=20.51ms max=499.45ms count=4
2025-08-28 19:36:58.764 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=500.41ms min=498.21ms max=502.61ms count=2
2025-08-28 19:36:59.766 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=31.57ms min=10.41ms max=497.54ms count=31
2025-08-28 19:36:59.827 16338-16338 LoginActivity           com.example.repairorderapp           D  开始获取加密密钥...
2025-08-28 19:36:59.827 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getEncryptionKey
2025-08-28 19:36:59.828 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/key
2025-08-28 19:36:59.828 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-08-28 19:36:59.828 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-08-28 19:36:59.830 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-08-28 19:36:59.830 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-08-28 19:36:59.831 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:36:59.831 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-08-28 19:36:59.832 16338-16792 TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-08-28 19:36:59.832 16338-16792 TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-08-28 19:36:59.832 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-08-28 19:36:59.832 16338-16792 TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-08-28 19:36:59.833 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:36:59.833 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:36:59.833 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:36:59.917 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/key (87ms)
2025-08-28 19:36:59.917 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:36:59.917 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:01 GMT
2025-08-28 19:36:59.917 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:36:59.918 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:36:59.918 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:36:59.918 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"cc4c4d36-9f59-4baa-a052-8e97a693313c","second":"04cb4434108b3f49f86a88202ee900a1f29684474cd154e6419a788dc6e1d2862e1ee39ba683d2081eb5750337e61be251dcc45eb3529ffa71f257d38a424f92f1"}}
2025-08-28 19:36:59.918 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (225-byte body)
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  密钥响应: {"code":200,"message":"ok","data":{"first":"cc4c4d36-9f59-4baa-a052-8e97a693313c","second":"04cb4434108b3f49f86a88202ee900a1f29684474cd154e6419a788dc6e1d2862e1ee39ba683d2081eb5750337e61be251dcc45eb3529ffa71f257d38a424f92f1"}}
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  密钥token: cc4c4d36-9f59-4baa-a052-8e97a693313c
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  公钥: 04cb4434108b3f49f86a88202ee900a1f29684474cd154e6419a788dc6e1d2862e1ee39ba683d2081eb5750337e61be251dcc45eb3529ffa71f257d38a424f92f1
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  开始登录流程...
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  用户名: B0000003
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码: 35htr
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  验证码token: 6864a3fd-b26f-4cde-9927-09611ff1b50e
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  密码token: cc4c4d36-9f59-4baa-a052-8e97a693313c
2025-08-28 19:36:59.922 16338-16338 LoginActivity           com.example.repairorderapp           D  开始使用SM2加密密码...
2025-08-28 19:36:59.923 16338-16338 LoginActivity           com.example.repairorderapp           D  处理后的公钥长度: 130, 公钥前10个字符: 04cb443410
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  加密前数据长度: 10
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  格式化后的公钥前10个字符: 04cb443410
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  创建SM2曲线参数
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  解码公钥
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  公钥字节长度: 65
2025-08-28 19:36:59.955 16338-16338 LoginActivity           com.example.repairorderapp           D  创建SM2加密引擎
2025-08-28 19:36:59.956 16338-16338 LoginActivity           com.example.repairorderapp           D  执行SM2加密
2025-08-28 19:36:59.971 16338-16338 LoginActivity           com.example.repairorderapp           D  加密完成，密文长度: 107
2025-08-28 19:36:59.971 16338-16338 LoginActivity           com.example.repairorderapp           D  密码加密完成，加密后长度: 214, 加密后前20个字符: 04b4b1fd4fed5952af6c
2025-08-28 19:36:59.971 16338-16338 LoginActivity           com.example.repairorderapp           D  密码加密完成
2025-08-28 19:36:59.972 16338-16338 LoginActivity           com.example.repairorderapp           D  发送登录请求...
2025-08-28 19:36:59.973 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_login
2025-08-28 19:36:59.977 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-28 19:36:59.978 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-28 19:36:59.979 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 374
2025-08-28 19:36:59.979 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"password":"04b4b1fd4fed5952af6c2ce4b9fa580ab632fc3a8149e2ee4be1ca40135d5afc3f023772a991df10fd0a01b032d95063a85f1346cb3a19983e8df69473a615ab7887156776165d81bac8a42c41b46ab4d49152b546c0df9a8feb1ae2f0255402b62c6791bd919b1c9228c7","code":"B0000003","passwordToken":"cc4c4d36-9f59-4baa-a052-8e97a693313c","captcha":"35htr","captchaToken":"6864a3fd-b26f-4cde-9927-09611ff1b50e"}
2025-08-28 19:36:59.980 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (374-byte body)
2025-08-28 19:36:59.980 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-28 19:36:59.980 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-28 19:36:59.980 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:36:59.981 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:36:59.982 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/wechat/staff/app/login (160ms)
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:01 GMT
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"40de8657-dbbe-4ca9-8ba3-8b105547894d","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-08-28 19:37:00.141 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (177-byte body)
2025-08-28 19:37:00.146 16338-16338 LoginActivity           com.example.repairorderapp           D  登录响应: {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"40de8657-dbbe-4ca9-8ba3-8b105547894d","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-08-28 19:37:00.146 16338-16338 LoginActivity           com.example.repairorderapp           D  登录成功
2025-08-28 19:37:00.146 16338-16338 TokenInterceptor        com.example.repairorderapp           D  Token过期状态已重置
2025-08-28 19:37:00.151 16338-16338 LoginActivity           com.example.repairorderapp           D  保存令牌信息:
2025-08-28 19:37:00.152 16338-16338 LoginActivity           com.example.repairorderapp           D  - userId: 1730205532934926338
2025-08-28 19:37:00.152 16338-16338 LoginActivity           com.example.repairorderapp           D  - userCode: B0000003
2025-08-28 19:37:00.153 16338-16338 LoginActivity           com.example.repairorderapp           D  - userName: 苏应来
2025-08-28 19:37:00.153 16338-16338 LoginActivity           com.example.repairorderapp           D  - engineerId: 1730205532934926338
2025-08-28 19:37:00.153 16338-16338 LoginActivity           com.example.repairorderapp           D  保存Token信息，不设置本地过期时间
2025-08-28 19:37:00.153 16338-16338 LoginActivity           com.example.repairorderapp           D  服务器响应数据: {"isSubscribed":false,"token":"40de8657-dbbe-4ca9-8ba3-8b105547894d","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}
2025-08-28 19:37:00.173 16338-16338 LoginActivity           com.example.repairorderapp           D  令牌保存验证: 成功
2025-08-28 19:37:00.173 16338-16338 RepairOrderApp          com.example.repairorderapp           D  COS SDK 已初始化，跳过重复初始化
2025-08-28 19:37:00.175 16338-16338 LoginActivity           com.example.repairorderapp           D  保存登录凭据 - 用户名: B0000003, 记住密码: true
2025-08-28 19:37:00.176 16338-16338 LoginActivity           com.example.repairorderapp           D  登录令牌保存成功
2025-08-28 19:37:00.184 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getResources
2025-08-28 19:37:00.186 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-28 19:37:00.186 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 19:37:00.186 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-28 19:37:00.188 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-28 19:37:00.189 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:00.189 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:00.190 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:00.190 16338-16792 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:00.191 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:00.192 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.277 16338-16338 LoginActivity           com.example.repairorderapp           D  设备信息上传前Token验证: 可用
2025-08-28 19:37:00.277 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-08-28 19:37:00.278 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/system/resources (89ms)
2025-08-28 19:37:00.280 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:00.281 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:01 GMT
2025-08-28 19:37:00.282 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:37:00.283 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-08-28 19:37:00.284 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-08-28 19:37:00.285 16338-16338 DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-08-28 19:37:00.289 16338-16343 .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 10MB AllocSpace bytes, 55(1236KB) LOS objects, 49% free, 11MB/23MB, paused 1.064ms,20.820ms total 129.886ms
2025-08-28 19:37:00.301 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:37:00.301 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:00.302 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":[{"label":"工单管理","value":"/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"/disassembly","type":{"value":"menu","label":"菜单"}}]}
2025-08-28 19:37:00.302 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3722-byte body)
2025-08-28 19:37:00.428 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-08-28 19:37:00.440 16338-16338 LoginActivity           com.example.repairorderapp           D  获取到权限数据: 23 项
2025-08-28 19:37:00.447 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-08-28 19:37:00.453 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-08-28 19:37:00.453 16338-16338 LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-08-28 19:37:00.459 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-08-28 19:37:00.466 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-08-28 19:37:00.467 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-08-28 19:37:00.468 16338-16338 DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-08-28 19:37:00.567 16338-16338 DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-08-28 19:37:00.573 16338-16338 LoginActivity           com.example.repairorderapp           I  登录成功，远程配置更新已触发
2025-08-28 19:37:00.573 16338-16338 LoginActivity           com.example.repairorderapp           D  权限加载完成，正在跳转到主页
2025-08-28 19:37:00.573 16338-16508 RemoteConfigManager     com.example.repairorderapp           I  🚀 登录成功，开始更新远程配置...
2025-08-28 19:37:00.574 16338-16508 RemoteConfigManager     com.example.repairorderapp           I  🚀 开始请求配置: userId=1730205532934926338, deviceId=cf7f6ce27817ef1a, appVersion=1.0.3-debug, hasToken=true
2025-08-28 19:37:00.578 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-28 19:37:00.580 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  X-User-Id: 1730205532934926338
2025-08-28 19:37:00.580 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  X-Device-Id: cf7f6ce27817ef1a
2025-08-28 19:37:00.583 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  X-App-Version: 1.0.3-debug
2025-08-28 19:37:00.583 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 19:37:00.583 16338-16792 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-28 19:37:00.583 16338-16792 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-28 19:37:00.583 16338-16792 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:00.583 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:00.583 16338-16792 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:00.585 16338-16792 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:00.585 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:00.586 16338-16792 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-User-Id: 1730205532934926338
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-Device-Id: cf7f6ce27817ef1a
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-App-Version: 1.0.3-debug
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:00.589 16338-16792 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.617 16338-16338 DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-08-28 19:37:00.617 16338-16338 DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-08-28 19:37:00.617 16338-16338 DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-08-28 19:37:00.622 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.623 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-28 19:37:00.623 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1061
2025-08-28 19:37:00.623 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1623330816,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756381020426}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-08-28 19:37:00","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-08-28 19:37:00","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"Asia/Shanghai","totalMemory":2067394560,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-08-28 19:37:00.625 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1061-byte body)
2025-08-28 19:37:00.627 16338-16754 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.627 16338-16754 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.627 16338-16754 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:00.627 16338-16754 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:00.627 16338-16754 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:00.628 16338-16338 LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-08-28 19:37:00.629 16338-16338 DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-08-28 19:37:00.631 16338-16338 DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-08-28 19:37:00.632 16338-16338 DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-08-28 19:37:00.632 16338-16754 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:00.632 16338-16754 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:00.632 16338-16754 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.632 16338-16754 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:00.633 16338-16754 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:00.633 16338-16754 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.633 16338-16754 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:00.633 16338-16754 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.645 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.646 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-28 19:37:00.646 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity暂停: LoginActivity
2025-08-28 19:37:00.651 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1061
2025-08-28 19:37:00.651 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1623326720,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756381020566}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-08-28 19:37:00","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-08-28 19:37:00","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"Asia/Shanghai","totalMemory":2067394560,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-08-28 19:37:00.652 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1061-byte body)
2025-08-28 19:37:00.652 16338-16791 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:00.653 16338-16791 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:00.655 16338-16791 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.656 16338-16791 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:00.656 16338-16791 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:00.656 16338-16791 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.657 16338-16791 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:00.657 16338-16791 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.671 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity创建: MainActivity
2025-08-28 19:37:00.675 16338-16338 RepairOrderApp          com.example.repairorderapp           D  为 MainActivity 设置全局触摸监听
2025-08-28 19:37:00.690 16338-16338 PermissionManager       com.example.repairorderapp           D  原始权限JSON数据: [{"label":"工单管理","value":"\/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"\/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"\/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"\/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"\/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"\/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"\/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"\/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"\/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"\/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"\/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"\/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"\/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"\/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"\/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"\/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"\/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"\/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"\/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"\/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"\/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"\/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"\/disassembly","type":{"value":"menu","label":"菜单"}}]
2025-08-28 19:37:00.699 16338-16338 MainActivity            com.example.repairorderapp           D  用户已登录，启动位置服务
2025-08-28 19:37:00.699 16338-16338 MainActivity            com.example.repairorderapp           D  位置服务状态:
位置服务开关: ✓ 启用
服务运行状态: ✓ 运行中
配置文件状态: ✓ 正常
配置项数量: 1
location_service_enabled = true
基础位置权限: ✓
后台位置权限: ✓
系统定位服务: ✓
2025-08-28 19:37:00.699 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-08-28 19:37:00.705 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-08-28 19:37:00.709 16338-16338 LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-08-28 19:37:00.709 16338-16338 MainActivity            com.example.repairorderapp           D  应用启动完成 - 调试模式
2025-08-28 19:37:00.712 16338-16338 PerformanceMonitor      com.example.repairorderapp           I  性能监控: page_load - 耗时: 31ms [耗时: 31ms] [内存: 14351KB]
2025-08-28 19:37:00.713 16338-16338 MainActivity_onCreate   com.example.repairorderapp           I  内存快照 - 使用: 14MB/192MB (7%) [内存: 14384KB]
2025-08-28 19:37:00.716 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity开始: MainActivity
2025-08-28 19:37:00.733 16338-16338 PermissionManager       com.example.repairorderapp           D  注册权限观察者，当前观察者数量: 4
2025-08-28 19:37:00.733 16338-16338 ProfileFragment         com.example.repairorderapp           D  权限已预加载，直接初始化功能按钮
2025-08-28 19:37:00.810 16338-16338 WindowOnBackDispatcher  com.example.repairorderapp           W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-08-28 19:37:00.811 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity恢复: MainActivity
2025-08-28 19:37:00.814 16338-16338 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderCount
2025-08-28 19:37:00.818 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-28 19:37:00.818 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 40de8657-dbbe-4ca9-8ba3-8b105547894d
2025-08-28 19:37:00.818 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 19:37:00.820 16338-16845 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-28 19:37:00.821 16338-16845 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-28 19:37:00.821 16338-16845 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:00.821 16338-16845 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:00.821 16338-16845 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:00.822 16338-16845 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:00.822 16338-16845 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:00.822 16338-16845 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.823 16338-16845 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:00.823 16338-16845 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:00.823 16338-16845 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:00.823 16338-16845 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:00.823 16338-16845 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:00.840 16338-16338 LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-08-28 19:37:00.948 16338-16754 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.948 16338-16754 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.948 16338-16754 TokenInterceptor        com.example.repairorderapp           I  Token过期次数: 1/3
2025-08-28 19:37:00.949 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (322ms)
2025-08-28 19:37:00.950 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:00.950 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:02 GMT
2025-08-28 19:37:00.950 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-28 19:37:00.951 16338-16791 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.951 16338-16791 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-28 19:37:00.951 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-28 19:37:00.951 16338-16791 TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余冷却时间: 9秒)
2025-08-28 19:37:00.951 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 46739728aca94f33ba704527a5642283
2025-08-28 19:37:00.951 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:00.951 16338-16792 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-28 19:37:00.951 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (299ms)
2025-08-28 19:37:00.952 16338-16792 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:00.952 16338-16792 TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/config/get (剩余冷却时间: 9秒)
2025-08-28 19:37:00.952 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:02 GMT
2025-08-28 19:37:00.952 16338-16754 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/config/get (368ms)
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: f91b82e2ac804f198c25b578c4fb0a04
2025-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:00.952 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:02 GMT
2025-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-28 19:37:00.952 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 6d6922ec4cb142d4aadd6d299ca61b2a
2025-08-28 19:37:00.953 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:00.953 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-28 19:37:00.953 16338-16791 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-28 19:37:00.955 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-28 19:37:00.955 16338-16792 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-28 19:37:00.956 16338-16508 RemoteConfigManager     com.example.repairorderapp           I  ✅ API调用成功: code=200
2025-08-28 19:37:00.956 16338-16508 RemoteConfigManager     com.example.repairorderapp           W  ❌ API响应数据为空: success=false, data=false
2025-08-28 19:37:00.956 16338-16508 RemoteConfigManager     com.example.repairorderapp           W  ⚠️ 登录后配置更新失败，将使用本地缓存配置
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (232ms)
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:02 GMT
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:37:01.052 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:01.054 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"pendingOrdersCount":"0","myWorkOrderCount":"0","appealOrderCount":"0"}}
2025-08-28 19:37:01.055 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (107-byte body)
2025-08-28 19:37:01.085 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=40.71ms min=2.19ms max=625.81ms count=27
2025-08-28 19:37:01.292 16338-16338 TokenInterceptor        com.example.repairorderapp           D  获取当前Activity: MainActivity
2025-08-28 19:37:01.292 16338-16338 TokenInterceptor        com.example.repairorderapp           I  准备显示Token过期对话框 (第1次)，当前Activity: MainActivity
2025-08-28 19:37:01.326 16338-16338 TokenInterceptor        com.example.repairorderapp           I  Token过期对话框显示成功 (第1次)
2025-08-28 19:37:01.327 16338-16338 LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-08-28 19:37:01.327 16338-16338 DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-08-28 19:37:01.327 16338-16338 LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-08-28 19:37:01.327 16338-16338 DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-08-28 19:37:01.327 16338-16338 LoginActivity           com.example.repairorderapp           I  登录成功，设备信息上传已触发
2025-08-28 19:37:01.329 16338-16338 ProfileFragment         com.example.repairorderapp           D  获取工单数量成功：待接工单=0, 我的工单=0, 申诉单=0
2025-08-28 19:37:02.018 16338-16338 VRI[LoginActivity]      com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-08-28 19:37:02.072 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity停止: LoginActivity
2025-08-28 19:37:02.081 16338-16338 RepairOrderApp          com.example.repairorderapp           D  Activity销毁: LoginActivity
2025-08-28 19:37:02.085 16338-16338 WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@a9ed3d1
2025-08-28 19:37:03.008 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=103.70ms min=1.55ms max=1241.81ms count=14
2025-08-28 19:37:03.454 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-08-28 19:37:03.707 16338-16338 LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-08-28 19:37:03.878 16338-16338 UpdateManager           com.example.repairorderapp           D  开始检查更新...
2025-08-28 19:37:03.882 16338-16362 UpdateRepository        com.example.repairorderapp           D  开始检查更新，当前版本: 1
2025-08-28 19:37:03.882 16338-16362 UpdateRepository        com.example.repairorderapp           D  更新检查参数: versionCode=1, versionName=1.0.3-debug, deviceId=cf7f6ce27817ef1a, userId=1730205532934926338
2025-08-28 19:37:03.883 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-28 19:37:03.883 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-28 19:37:03.883 16338-16845 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-28 19:37:03.884 16338-16845 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-28 19:37:03.884 16338-16845 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-28 19:37:03.884 16338-16845 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-28 19:37:03.884 16338-16845 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-28 19:37:03.884 16338-16845 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 40de8657-d...
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-28 19:37:03.885 16338-16845 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-28 19:37:03.954 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338 (71ms)
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Thu, 28 Aug 2025 11:37:05 GMT
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"hasUpdate":false}}
2025-08-28 19:37:03.955 16338-16845 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (54-byte body)
2025-08-28 19:37:03.957 16338-16362 UpdateRepository        com.example.repairorderapp           D  更新检查成功: hasUpdate=false
2025-08-28 19:37:03.958 16338-16362 UpdateRepository        com.example.repairorderapp           D  当前已是最新版本或无权限更新
2025-08-28 19:37:03.959 16338-16338 UpdateManager           com.example.repairorderapp           D  当前已是最新版本
2025-08-28 19:37:03.959 16338-16338 MainActivity            com.example.repairorderapp           D  当前已是最新版本
2025-08-28 19:37:04.528 16338-16396 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=126.43ms min=7.75ms max=1338.99ms count=12
2025-08-28 19:37:29.681 16338-16508 EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 2条
2025-08-28 19:37:32.483 16338-16362 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
