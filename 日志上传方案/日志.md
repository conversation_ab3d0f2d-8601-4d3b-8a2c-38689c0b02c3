2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  密钥响应: {"code":200,"message":"ok","data":{"first":"856de10f-bcae-4c4c-b0da-48cd1b155302","second":"04b7a36397c2e949b03544a2a43849903fbed97d269c2acdd198a1966274bf756c8d74b8bcfdef0d7d95f756c8d914a6e4fd3a30570498151476df73feb51df510"}}
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  密钥token: 856de10f-bcae-4c4c-b0da-48cd1b155302
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  公钥: 04b7a36397c2e949b03544a2a43849903fbed97d269c2acdd198a1966274bf756c8d74b8bcfdef0d7d95f756c8d914a6e4fd3a30570498151476df73feb51df510
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  开始登录流程...
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  用户名: B0000003
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  验证码: cuucp
2025-08-29 09:51:21.477 19151-19151 LoginActivity           com.example.repairorderapp           D  验证码token: c19e2344-1f71-4932-877e-cc426e56358b
2025-08-29 09:51:21.478 19151-19151 LoginActivity           com.example.repairorderapp           D  密码token: 856de10f-bcae-4c4c-b0da-48cd1b155302
2025-08-29 09:51:21.478 19151-19151 LoginActivity           com.example.repairorderapp           D  开始使用SM2加密密码...
2025-08-29 09:51:21.478 19151-19151 LoginActivity           com.example.repairorderapp           D  处理后的公钥长度: 130, 公钥前10个字符: 04b7a36397
2025-08-29 09:51:21.523 19151-19151 LoginActivity           com.example.repairorderapp           D  加密前数据长度: 10
2025-08-29 09:51:21.523 19151-19151 LoginActivity           com.example.repairorderapp           D  格式化后的公钥前10个字符: 04b7a36397
2025-08-29 09:51:21.523 19151-19151 LoginActivity           com.example.repairorderapp           D  创建SM2曲线参数
2025-08-29 09:51:21.524 19151-19151 LoginActivity           com.example.repairorderapp           D  解码公钥
2025-08-29 09:51:21.524 19151-19151 LoginActivity           com.example.repairorderapp           D  公钥字节长度: 65
2025-08-29 09:51:21.527 19151-19151 LoginActivity           com.example.repairorderapp           D  创建SM2加密引擎
2025-08-29 09:51:21.530 19151-19151 LoginActivity           com.example.repairorderapp           D  执行SM2加密
2025-08-29 09:51:21.566 19151-19151 LoginActivity           com.example.repairorderapp           D  加密完成，密文长度: 107
2025-08-29 09:51:21.566 19151-19151 LoginActivity           com.example.repairorderapp           D  密码加密完成，加密后长度: 214, 加密后前20个字符: 04ec07c26fdb4f51b115
2025-08-29 09:51:21.566 19151-19151 LoginActivity           com.example.repairorderapp           D  密码加密完成
2025-08-29 09:51:21.566 19151-19151 LoginActivity           com.example.repairorderapp           D  发送登录请求...
2025-08-29 09:51:21.566 19151-19151 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_login
2025-08-29 09:51:21.573 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-29 09:51:21.573 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-29 09:51:21.574 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 374
2025-08-29 09:51:21.576 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"password":"04ec07c26fdb4f51b1152c89c3203a656ac5d0d19f9c1c1669c3c20b23f5220ec9496b58c0556b82fe9b454701e77de14f47b780853ce53596469a877d60ba9d0790cb4a3e4d4819d4d2f6647aa4cdc59b5f94bbd1e41d0e32d6f33030ddb754325b8dcf0b5151c648e16a","code":"B0000003","passwordToken":"856de10f-bcae-4c4c-b0da-48cd1b155302","captcha":"cuucp","captchaToken":"c19e2344-1f71-4932-877e-cc426e56358b"}
2025-08-29 09:51:21.577 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (374-byte body)
2025-08-29 09:51:21.577 19151-19804 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-29 09:51:21.578 19151-19804 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-08-29 09:51:21.579 19151-19804 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-08-29 09:51:21.581 19151-19804 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:21.582 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:21.582 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:21.622 19151-19167 .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 10MB AllocSpace bytes, 17(440KB) LOS objects, 49% free, 9902KB/19MB, paused 1.026ms,7.550ms total 70.763ms
2025-08-29 09:51:21.681 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=9.00ms min=1.82ms max=25.41ms count=55
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/wechat/staff/app/login (157ms)
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:20 GMT
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"513cdf0a-01da-4c3f-b11b-6e089687089a","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-08-29 09:51:21.735 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (177-byte body)
2025-08-29 09:51:21.742 19151-19151 LoginActivity           com.example.repairorderapp           D  登录响应: {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"513cdf0a-01da-4c3f-b11b-6e089687089a","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-08-29 09:51:21.742 19151-19151 LoginActivity           com.example.repairorderapp           D  登录成功
2025-08-29 09:51:21.743 19151-19151 TokenInterceptor        com.example.repairorderapp           D  Token过期状态已重置，开始登录保护期
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  保存令牌信息:
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  - userId: 1730205532934926338
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  - userCode: B0000003
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  - userName: 苏应来
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  - engineerId: 1730205532934926338
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  保存Token信息，不设置本地过期时间
2025-08-29 09:51:21.752 19151-19151 LoginActivity           com.example.repairorderapp           D  服务器响应数据: {"isSubscribed":false,"token":"513cdf0a-01da-4c3f-b11b-6e089687089a","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}
2025-08-29 09:51:21.772 19151-19151 LoginActivity           com.example.repairorderapp           D  令牌保存验证: 成功
2025-08-29 09:51:21.773 19151-19151 RepairOrderApp          com.example.repairorderapp           D  COS SDK 已初始化，跳过重复初始化
2025-08-29 09:51:21.775 19151-19151 LoginActivity           com.example.repairorderapp           D  保存登录凭据 - 用户名: B0000003, 记住密码: true
2025-08-29 09:51:21.775 19151-19151 LoginActivity           com.example.repairorderapp           D  登录令牌保存成功
2025-08-29 09:51:21.778 19151-19151 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getResources
2025-08-29 09:51:21.796 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-29 09:51:21.797 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-29 09:51:21.797 19151-19804 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:21.797 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:21.798 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:21.859 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/system/resources (62ms)
2025-08-29 09:51:21.859 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:21.860 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:21 GMT
2025-08-29 09:51:21.860 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-29 09:51:21.860 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-29 09:51:21.860 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:21.863 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":[{"label":"工单管理","value":"/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"/disassembly","type":{"value":"menu","label":"菜单"}}]}
2025-08-29 09:51:21.863 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3722-byte body)
2025-08-29 09:51:21.873 19151-19151 LoginActivity           com.example.repairorderapp           D  获取到权限数据: 23 项
2025-08-29 09:51:21.880 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-08-29 09:51:21.890 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-08-29 09:51:21.894 19151-19151 LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-08-29 09:51:21.897 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-08-29 09:51:21.899 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-08-29 09:51:21.899 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-08-29 09:51:21.899 19151-19151 DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-08-29 09:51:21.995 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-08-29 09:51:22.000 19151-19151 LoginActivity           com.example.repairorderapp           I  登录成功，远程配置更新已触发
2025-08-29 09:51:22.000 19151-19151 LoginActivity           com.example.repairorderapp           D  权限加载完成，正在跳转到主页
2025-08-29 09:51:22.001 19151-19270 RemoteConfigManager     com.example.repairorderapp           I  🚀 登录成功，开始更新远程配置...
2025-08-29 09:51:22.002 19151-19270 RemoteConfigManager     com.example.repairorderapp           I  🚀 开始请求配置: userId=1730205532934926338, deviceId=cf7f6ce27817ef1a, appVersion=1.0.3-debug, hasToken=true
2025-08-29 09:51:22.006 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-29 09:51:22.006 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  X-User-Id: 1730205532934926338
2025-08-29 09:51:22.006 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  X-Device-Id: cf7f6ce27817ef1a
2025-08-29 09:51:22.014 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  X-App-Version: 1.0.3-debug
2025-08-29 09:51:22.015 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-29 09:51:22.015 19151-19804 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-29 09:51:22.015 19151-19804 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-29 09:51:22.016 19151-19804 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:22.018 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:22.019 19151-19804 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:22.019 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:22.020 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:22.020 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.020 19151-19804 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:22.020 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-User-Id: 1730205532934926338
2025-08-29 09:51:22.020 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-Device-Id: cf7f6ce27817ef1a
2025-08-29 09:51:22.028 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-App-Version: 1.0.3-debug
2025-08-29 09:51:22.029 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:22.029 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:22.030 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:22.031 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.038 19151-19151 LoginActivity           com.example.repairorderapp           D  设备信息上传前Token验证: 可用
2025-08-29 09:51:22.038 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-08-29 09:51:22.039 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-08-29 09:51:22.039 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-08-29 09:51:22.039 19151-19151 DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-08-29 09:51:22.087 19151-19804 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-29 09:51:22.087 19151-19804 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-08-29 09:51:22.087 19151-19804 TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/config/get (剩余保护时间: 29秒)
2025-08-29 09:51:22.089 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/config/get (73ms)
2025-08-29 09:51:22.090 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:22.090 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:21 GMT
2025-08-29 09:51:22.092 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 09:51:22.092 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 09:51:22.092 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: c2e0773f730140c08270b5a5573e16bb
2025-08-29 09:51:22.092 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:22.094 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 09:51:22.094 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 09:51:22.097 19151-19270 RemoteConfigManager     com.example.repairorderapp           I  ✅ API调用成功: code=200
2025-08-29 09:51:22.098 19151-19270 RemoteConfigManager     com.example.repairorderapp           W  ❌ API响应数据为空: success=false, data=false
2025-08-29 09:51:22.099 19151-19270 RemoteConfigManager     com.example.repairorderapp           W  ⚠️ 登录后配置更新失败，将使用本地缓存配置
2025-08-29 09:51:22.136 19151-19151 DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-08-29 09:51:22.142 19151-19151 LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-08-29 09:51:22.142 19151-19151 DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-08-29 09:51:22.142 19151-19151 DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-08-29 09:51:22.142 19151-19151 DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-08-29 09:51:22.145 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.145 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-29 09:51:22.146 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1061
2025-08-29 09:51:22.147 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity暂停: LoginActivity
2025-08-29 09:51:22.153 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1619275776,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756432281995}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-08-29 09:51:21","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-08-29 09:51:21","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"Asia/Shanghai","totalMemory":2067394560,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-08-29 09:51:22.154 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1061-byte body)
2025-08-29 09:51:22.154 19151-19804 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.154 19151-19804 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.154 19151-19804 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:22.155 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:22.155 19151-19804 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:22.157 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:22.158 19151-19804 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:22.159 19151-19804 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.162 19151-19804 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:22.164 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:22.165 19151-19804 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:22.165 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:22.165 19151-19804 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.177 19151-19151 DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-08-29 09:51:22.177 19151-19151 DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-08-29 09:51:22.177 19151-19151 DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-08-29 09:51:22.184 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.187 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-08-29 09:51:22.188 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1061
2025-08-29 09:51:22.189 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1619275776,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756432282135}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-08-29 09:51:22","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-08-29 09:51:22","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"Asia/Shanghai","totalMemory":2067394560,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-08-29 09:51:22.191 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1061-byte body)
2025-08-29 09:51:22.194 19151-19869 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.196 19151-19869 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.197 19151-19869 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:22.198 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:22.201 19151-19869 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:22.202 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:22.202 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:22.202 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.203 19151-19869 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:22.203 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:22.203 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:22.203 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:22.205 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity创建: MainActivity
2025-08-29 09:51:22.205 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.211 19151-19151 RepairOrderApp          com.example.repairorderapp           D  为 MainActivity 设置全局触摸监听
2025-08-29 09:51:22.227 19151-19804 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.227 19151-19804 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.227 19151-19804 TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-08-29 09:51:22.227 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (73ms)
2025-08-29 09:51:22.227 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:22.227 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:21 GMT
2025-08-29 09:51:22.227 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 09:51:22.228 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 09:51:22.228 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 55604e30f91d4d3e8746e98dd158e5c7
2025-08-29 09:51:22.228 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:22.229 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 09:51:22.230 19151-19804 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 09:51:22.236 19151-19151 PermissionManager       com.example.repairorderapp           D  原始权限JSON数据: [{"label":"工单管理","value":"\/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"\/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"\/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"\/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"\/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"\/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"\/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"\/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"\/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"\/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"\/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"\/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"\/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"\/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"\/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"\/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"\/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"\/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"\/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"\/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"\/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"\/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"\/disassembly","type":{"value":"menu","label":"菜单"}}]
2025-08-29 09:51:22.248 19151-19151 MainActivity            com.example.repairorderapp           D  用户已登录，启动位置服务
2025-08-29 09:51:22.248 19151-19151 MainActivity            com.example.repairorderapp           D  位置服务状态:
位置服务开关: ✓ 启用
服务运行状态: ✓ 运行中
配置文件状态: ✓ 正常
配置项数量: 1
location_service_enabled = true
基础位置权限: ✓
后台位置权限: ✓
系统定位服务: ✓
2025-08-29 09:51:22.249 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-08-29 09:51:22.263 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-08-29 09:51:22.267 19151-19151 LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-08-29 09:51:22.267 19151-19151 MainActivity            com.example.repairorderapp           D  应用启动完成 - 调试模式
2025-08-29 09:51:22.267 19151-19869 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.267 19151-19869 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-08-29 09:51:22.267 19151-19869 TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-08-29 09:51:22.268 19151-19151 PerformanceMonitor      com.example.repairorderapp           I  性能监控: page_load - 耗时: 54ms [耗时: 54ms] [内存: 12646KB]
2025-08-29 09:51:22.268 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (77ms)
2025-08-29 09:51:22.268 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:22.268 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:21 GMT
2025-08-29 09:51:22.268 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 09:51:22.268 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 09:51:22.269 19151-19151 MainActivity_onCreate   com.example.repairorderapp           I  内存快照 - 使用: 12MB/192MB (6%) [内存: 12679KB]
2025-08-29 09:51:22.271 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 40667a58528b4125a34b624612e2ea2c
2025-08-29 09:51:22.271 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:22.274 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 09:51:22.277 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 09:51:22.277 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity开始: MainActivity
2025-08-29 09:51:22.300 19151-19151 PermissionManager       com.example.repairorderapp           D  注册权限观察者，当前观察者数量: 3
2025-08-29 09:51:22.300 19151-19151 ProfileFragment         com.example.repairorderapp           D  权限已预加载，直接初始化功能按钮
2025-08-29 09:51:22.339 19151-19151 WindowOnBackDispatcher  com.example.repairorderapp           W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-08-29 09:51:22.344 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity恢复: MainActivity
2025-08-29 09:51:22.350 19151-19151 GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderCount
2025-08-29 09:51:22.354 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-29 09:51:22.355 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 513cdf0a-01da-4c3f-b11b-6e089687089a
2025-08-29 09:51:22.355 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-29 09:51:22.359 19151-19869 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:22.359 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:22.360 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:22.433 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (75ms)
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:21 GMT
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"pendingOrdersCount":"0","myWorkOrderCount":"0","appealOrderCount":"0"}}
2025-08-29 09:51:22.435 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (107-byte body)
2025-08-29 09:51:22.541 19151-19151 LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-08-29 09:51:22.542 19151-19151 DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-08-29 09:51:22.542 19151-19151 LoginActivity           com.example.repairorderapp           I  登录成功，设备信息上传已触发
2025-08-29 09:51:22.551 19151-19151 LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-08-29 09:51:22.552 19151-19151 LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-08-29 09:51:22.555 19151-19151 DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-08-29 09:51:22.612 19151-19151 ProfileFragment         com.example.repairorderapp           D  获取工单数量成功：待接工单=0, 我的工单=0, 申诉单=0
2025-08-29 09:51:22.687 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=82.65ms min=2.09ms max=559.77ms count=12
2025-08-29 09:51:22.864 19151-19151 InsetsController        com.example.repairorderapp           D  hide(ime(), fromIme=true)
2025-08-29 09:51:22.884 19151-19151 RemoteInpu...ectionImpl com.example.repairorderapp           W  requestCursorUpdates on inactive InputConnection
2025-08-29 09:51:22.910 19151-19151 ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:d5b2ea14: onCancelled at PHASE_CLIENT_ANIMATION_CANCEL
2025-08-29 09:51:22.911 19151-19151 ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:1f2c2314: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-08-29 09:51:22.916 19151-19151 ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:1f2c2314: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-08-29 09:51:23.201 19151-19151 VRI[LoginActivity]      com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-08-29 09:51:23.232 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity停止: LoginActivity
2025-08-29 09:51:23.237 19151-19151 RepairOrderApp          com.example.repairorderapp           D  Activity销毁: LoginActivity
2025-08-29 09:51:23.239 19151-19151 WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@184453
2025-08-29 09:51:23.268 19151-19269 EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 2条
2025-08-29 09:51:24.626 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=202.97ms min=6.53ms max=1661.28ms count=10
2025-08-29 09:51:24.860 19151-19270 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:51:24.891 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-08-29 09:51:25.265 19151-19151 LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-08-29 09:51:25.376 19151-19151 UpdateManager           com.example.repairorderapp           D  开始检查更新...
2025-08-29 09:51:25.381 19151-19270 UpdateRepository        com.example.repairorderapp           D  开始检查更新，当前版本: 1
2025-08-29 09:51:25.382 19151-19270 UpdateRepository        com.example.repairorderapp           D  更新检查参数: versionCode=1, versionName=1.0.3-debug, deviceId=cf7f6ce27817ef1a, userId=1730205532934926338
2025-08-29 09:51:25.383 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-29 09:51:25.386 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-08-29 09:51:25.388 19151-19869 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:51:25.388 19151-19869 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:51:25.389 19151-19869 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:25.390 19151-19869 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:51:25.390 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:51:25.390 19151-19869 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:51:25.390 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:51:25.390 19151-19869 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338 (106ms)
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:52:24 GMT
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"hasUpdate":false}}
2025-08-29 09:51:25.494 19151-19869 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (54-byte body)
2025-08-29 09:51:25.497 19151-19270 UpdateRepository        com.example.repairorderapp           D  更新检查成功: hasUpdate=false
2025-08-29 09:51:25.497 19151-19270 UpdateRepository        com.example.repairorderapp           D  当前已是最新版本或无权限更新
2025-08-29 09:51:25.501 19151-19151 UpdateManager           com.example.repairorderapp           D  当前已是最新版本
2025-08-29 09:51:25.502 19151-19151 MainActivity            com.example.repairorderapp           D  当前已是最新版本
2025-08-29 09:51:26.041 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=114.90ms min=1.56ms max=1247.02ms count=12
2025-08-29 09:51:26.941 19151-19151 WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@a1e0839
2025-08-29 09:51:26.961 19151-19300 HWUI                    com.example.repairorderapp           D  endAllActiveAnimators on 0x71405da319b0 (RippleDrawable) with handle 0x7140ede05c30
2025-08-29 09:51:27.049 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=402.98ms min=3.04ms max=2320.91ms count=6
2025-08-29 09:52:24.861 19151-19270 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:52:31.271 19151-19270 LocationUpdateService   com.example.repairorderapp           D  刷新唤醒锁
2025-08-29 09:52:31.272 19151-19270 LocationUpdateService   com.example.repairorderapp           D  已释放唤醒锁
2025-08-29 09:52:31.281 19151-19270 LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-08-29 09:52:31.281 19151-19270 LocationUpdateService   com.example.repairorderapp           D  唤醒锁刷新任务已取消
2025-08-29 09:53:24.862 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:54:24.863 19151-19363 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:55:24.866 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:55:24.952 19151-19269 LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-08-29 09:55:24.952 19151-19269 SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-08-29 09:55:24.952 19151-19269 SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-08-29 09:55:24.962 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-08-29 09:55:24.962 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 3
2025-08-29 09:55:24.965 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-08-29 09:55:24.966 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-08-29 09:55:24.970 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:24.970 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-08-29 09:55:24.970 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1740
2025-08-29 09:55:24.971 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432233993,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2784,\"memoryUsage\":12682192}","id":1069,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2784ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"PerformanceMonitor","timestamp":"2025-08-29 09:50:33","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432282268,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":54,\"memoryUsage\":12950448}","id":1070,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: page_load - 耗时: 54ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"PerformanceMonitor","timestamp":"2025-08-29 09:51:22","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432282271,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"memoryUsage\":12983968}","id":1071,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"内存快照 - 使用: 12MB/192MB (6%)","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"MainActivity_onCreate","timestamp":"2025-08-29 09:51:22","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-08-29 09:55:24.971 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1740-byte body)
2025-08-29 09:55:24.971 19151-19888 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:24.971 19151-19888 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:24.971 19151-19888 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:55:24.971 19151-19888 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:55:24.971 19151-19888 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:55:24.972 19151-19888 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:55:24.973 19151-19888 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:55:24.973 19151-19888 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:55:24.974 19151-19888 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:55:24.974 19151-19888 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:55:24.975 19151-19888 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:55:24.975 19151-19888 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:55:24.975 19151-19888 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:55:25.030 19151-19888 TrafficStats            com.example.repairorderapp           D  tagSocket(131) with statsTag=0xffffffff, statsUid=-1
2025-08-29 09:55:25.245 19151-19888 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.245 19151-19888 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.245 19151-19888 TokenInterceptor        com.example.repairorderapp           I  Token过期次数: 1/3
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (274ms)
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:56:24 GMT
2025-08-29 09:55:25.246 19151-19151 TokenInterceptor        com.example.repairorderapp           D  获取当前Activity: MainActivity
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 2dbdcf6b6ecd4473b1080140301a6b92
2025-08-29 09:55:25.246 19151-19151 TokenInterceptor        com.example.repairorderapp           I  准备显示Token过期对话框 (第1次)，当前Activity: MainActivity
2025-08-29 09:55:25.246 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:55:25.252 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 09:55:25.253 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 09:55:25.258 19151-19269 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (3条) - 响应: 会话过期
2025-08-29 09:55:25.258 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 1
2025-08-29 09:55:25.262 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-08-29 09:55:25.263 19151-19269 BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-08-29 09:55:25.265 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.266 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-08-29 09:55:25.266 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 607
2025-08-29 09:55:25.267 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432233994,"deviceId":"cf7f6ce27817ef1a","id":1068,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"LocationUpdateService","timestamp":"2025-08-29 09:50:33","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-08-29 09:55:25.267 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (607-byte body)
2025-08-29 09:55:25.267 19151-19888 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.267 19151-19888 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.267 19151-19888 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 09:55:25.267 19151-19888 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 09:55:25.267 19151-19888 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 09:55:25.267 19151-19888 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 09:55:25.268 19151-19888 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 09:55:25.285 19151-19151 TokenInterceptor        com.example.repairorderapp           I  Token过期对话框显示成功 (第1次)
2025-08-29 09:55:25.334 19151-19888 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.334 19151-19888 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 09:55:25.334 19151-19888 TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload (剩余冷却时间: 9秒)
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (66ms)
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 01:56:24 GMT
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: de7a4c2ca6c94cefa558f8521318afd2
2025-08-29 09:55:25.334 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 09:55:25.337 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 09:55:25.338 19151-19888 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 09:55:25.346 19151-19269 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (1条) - 响应: 会话过期
2025-08-29 09:55:25.346 19151-19269 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-08-29 09:55:25.377 19151-19269 LogUploadManager        com.example.repairorderapp           D  标记3条日志为已上传
2025-08-29 09:55:25.377 19151-19269 LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 3条
2025-08-29 09:55:25.385 19151-19269 LogUploadManager        com.example.repairorderapp           D  标记1条日志为已上传
2025-08-29 09:55:25.386 19151-19269 LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 1条
2025-08-29 09:55:25.386 19151-19269 LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 4条
2025-08-29 09:55:25.386 19151-19269 SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-08-29 09:55:25.386 19151-19269 LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-08-29 09:55:25.386 19151-19269 LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-08-29 09:55:25.446 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=34048.82ms min=4.03ms max=238282.70ms count=7
2025-08-29 09:55:26.911 19151-19300 EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=119.23ms min=2.62ms max=1283.54ms count=12
2025-08-29 09:55:30.415 19151-19269 LocationUpdateService   com.example.repairorderapp           D  前台服务通知已刷新
2025-08-29 09:55:31.220 19151-19363 LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-08-29 09:55:31.220 19151-19363 LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 距离上次更新已经过去 300 秒
2025-08-29 09:55:31.220 19151-19363 LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-08-29 09:55:31.246 19151-19363 LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-08-29 09:55:31.246 19151-19363 LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-08-29 09:55:31.246 19151-19363 LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 299 秒后进行下次更新
2025-08-29 09:55:33.257 19151-19353 BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-08-29 09:55:33.355 19151-19898 TrafficStats            com.example.repairorderapp           D  tagSocket(132) with statsTag=0xffffffff, statsUid=-1
2025-08-29 09:55:35.375 19151-19151 LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=1, 原因=ERROR_NETWORK
2025-08-29 09:55:35.375 19151-19151 PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 4155ms [耗时: 4155ms] [内存: 15809KB]
2025-08-29 09:55:35.382 19151-19151 LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 1 - ERROR_NETWORK
2025-08-29 09:55:35.383 19151-19151 LocationUpdateService   com.example.repairorderapp           D  与上次位置距离: 0.0米
2025-08-29 09:55:35.383 19151-19151 LocationUpdateService   com.example.repairorderapp           I  设备已静止超过10分钟，切换到低功耗定位模式。
2025-08-29 09:55:35.385 19151-19151 LocationUpdateService   com.example.repairorderapp           D  位置请求配置已更新: GPS=true, 精度=3, 间隔=1800000ms
2025-08-29 09:55:35.388 19151-19363 LocationUpdateService   com.example.repairorderapp           D  定时位置更新任务已取消
2025-08-29 09:55:35.390 19151-19151 LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-08-29 09:55:35.391 19151-19270 LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用30分钟更新间隔
2025-08-29 09:55:35.392 19151-19270 LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 1795 秒后进行下次更新
2025-08-29 09:55:35.394 19151-19151 LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-08-29 09:55:35.394 19151-19151 LocationUpdateService   com.example.repairorderapp           D  屏幕状态变化，更新间隔调整为: 1800秒
2025-08-29 09:55:53.296 19151-19270 EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 1条
2025-08-29 09:56:24.867 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:56:31.282 19151-19269 LocationUpdateService   com.example.repairorderapp           D  刷新唤醒锁
2025-08-29 09:56:31.287 19151-19269 LocationUpdateService   com.example.repairorderapp           D  已释放唤醒锁
2025-08-29 09:56:31.296 19151-19269 LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-08-29 09:56:31.296 19151-19269 LocationUpdateService   com.example.repairorderapp           D  唤醒锁刷新任务已取消
2025-08-29 09:57:05.394 19151-19269 LocationUpdateService   com.example.repairorderapp           W  位置回调超时，重置标志并重新请求定位
2025-08-29 09:57:05.394 19151-19269 LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-08-29 09:57:05.411 19151-19353 BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-08-29 09:57:05.415 19151-19269 LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-08-29 09:57:07.493 19151-19963 TrafficStats            com.example.repairorderapp           D  tagSocket(135) with statsTag=0xffffffff, statsUid=-1
2025-08-29 09:57:08.288 19151-19151 LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=404, 原因=ERROR_SERVER_NOT_LOCATION
2025-08-29 09:57:08.288 19151-19151 PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 2894ms [耗时: 2894ms] [内存: 16247KB]
2025-08-29 09:57:08.291 19151-19151 LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 404 - ERROR_SERVER_NOT_LOCATION
2025-08-29 09:57:08.291 19151-19151 LocationUpdateService   com.example.repairorderapp           D  与上次位置距离: 0.0米
2025-08-29 09:57:23.314 19151-19270 EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 1条
2025-08-29 09:57:24.868 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:58:24.869 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 09:59:24.870 19151-19270 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 10:00:24.871 19151-19269 GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-08-29 10:00:25.387 19151-19269 LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-08-29 10:00:25.387 19151-19269 SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-08-29 10:00:25.387 19151-19269 SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-08-29 10:00:25.399 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-08-29 10:00:25.399 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 2
2025-08-29 10:00:25.404 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-08-29 10:00:25.404 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-08-29 10:00:25.408 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.409 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-08-29 10:00:25.409 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1211
2025-08-29 10:00:25.410 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432535381,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":4155,\"memoryUsage\":16188912}","id":1073,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 4155ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"PerformanceMonitor","timestamp":"2025-08-29 09:55:35","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432628288,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2894,\"memoryUsage\":16637600}","id":1075,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2894ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"PerformanceMonitor","timestamp":"2025-08-29 09:57:08","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-08-29 10:00:25.410 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1211-byte body)
2025-08-29 10:00:25.411 19151-20016 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.411 19151-20016 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.411 19151-20016 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 10:00:25.411 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 10:00:25.412 19151-20016 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 10:00:25.412 19151-20016 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 10:00:25.412 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 10:00:25.413 19151-20016 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 10:00:25.413 19151-20016 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 10:00:25.413 19151-20016 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 10:00:25.413 19151-20016 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 10:00:25.413 19151-20016 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 10:00:25.414 19151-20016 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 10:00:25.502 19151-20016 TrafficStats            com.example.repairorderapp           D  tagSocket(133) with statsTag=0xffffffff, statsUid=-1
2025-08-29 10:00:25.706 19151-20016 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.706 19151-20016 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.706 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token过期对话框已显示，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (295ms)
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 02:01:24 GMT
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 10:00:25.706 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: f6e5732d339046a29fe9900a00718287
2025-08-29 10:00:25.707 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 10:00:25.708 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 10:00:25.710 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 10:00:25.713 19151-19270 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (2条) - 响应: 会话过期
2025-08-29 10:00:25.713 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 2
2025-08-29 10:00:25.718 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-08-29 10:00:25.718 19151-19270 BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-08-29 10:00:25.719 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.719 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-08-29 10:00:25.719 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1133
2025-08-29 10:00:25.720 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432535382,"deviceId":"cf7f6ce27817ef1a","id":1072,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d1, 原因\u003dERROR_NETWORK","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"LocationUpdateService","timestamp":"2025-08-29 09:55:35","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756432628290,"deviceId":"cf7f6ce27817ef1a","id":1074,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"06010e16-6083-4673-a12b-58311721afc4","tag":"LocationUpdateService","timestamp":"2025-08-29 09:57:08","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-08-29 10:00:25.720 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1133-byte body)
2025-08-29 10:00:25.721 19151-20016 ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.721 19151-20016 TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.721 19151-20016 TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-08-29 10:00:25.721 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-08-29 10:00:25.721 19151-20016 TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 513cdf0a-0...
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-08-29 10:00:25.722 19151-20016 TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-08-29 10:00:25.723 19151-20016 TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-08-29 10:00:25.773 19151-20016 TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.773 19151-20016 TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.773 19151-20016 TokenInterceptor        com.example.repairorderapp           D  Token过期对话框已显示，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (52ms)
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  date: Fri, 29 Aug 2025 02:01:25 GMT
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 3b1b825fff4e4f4e9229d48f8d355af3
2025-08-29 10:00:25.773 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-08-29 10:00:25.774 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-08-29 10:00:25.775 19151-20016 okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-08-29 10:00:25.778 19151-19270 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (2条) - 响应: 会话过期
2025-08-29 10:00:25.778 19151-19270 BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-08-29 10:00:25.787 19151-19270 LogUploadManager        com.example.repairorderapp           D  标记2条日志为已上传
2025-08-29 10:00:25.787 19151-19270 LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 2条
2025-08-29 10:00:25.795 19151-19270 LogUploadManager        com.example.repairorderapp           D  标记2条日志为已上传
2025-08-29 10:00:25.795 19151-19270 LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 2条
2025-08-29 10:00:25.795 19151-19270 LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 4条
2025-08-29 10:00:25.795 19151-19270 SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-08-29 10:00:25.795 19151-19270 LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-08-29 10:00:25.795 19151-19270 LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-08-29 10:00:30.419 19151-19270 LocationUpdateService   com.example.repairorderapp           D  前台服务通知已刷新
2025-08-29 10:00:31.301 19151-19269 LocationUpdateService   com.example.repairorderapp           D  刷新唤醒锁
2025-08-29 10:00:31.301 19151-19269 LocationUpdateService   com.example.repairorderapp           D  已释放唤醒锁
2025-08-29 10:00:31.310 19151-19269 LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-08-29 10:00:31.311 19151-19269 LocationUpdateService   com.example.repairorderapp           D  唤醒锁刷新任务已取消