--------- beginning of main
--------- beginning of system
2025-09-01 14:38:30.313  4206-4288  <PERSON><PERSON>                  pid-4206                             E  [62] ItemStore: getItems RPC failed for item com.example.repairorderapp
2025-09-01 14:40:44.288  4671-4842  beacon                  pid-4671                             E  BeaconSDK init success! appkey is: 0AND05KOZX0E3L2H, packageName is: com.example.repairorderapp
--------- beginning of crash
2025-09-01 18:27:31.328   606-1918  AppOps                  system_server                        E  Operation not started: uid=10228 pkg=com.example.repairorderapp(null) op=WAKE_LOCK
---------------------------- PROCESS STARTED (8985) for package com.example.repairorderapp ----------------------------
2025-09-01 18:33:26.093  8985-8985  ApplicationLoaders      com.example.repairorderapp           D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
2025-09-01 18:33:26.093  8985-8985  ApplicationLoaders      com.example.repairorderapp           D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.sidecar.jar
2025-09-01 18:33:26.103  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes14.dm': No such file or directory
2025-09-01 18:33:26.106  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes10.dm': No such file or directory
2025-09-01 18:33:26.113  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
2025-09-01 18:33:26.116  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes12.dm': No such file or directory
2025-09-01 18:33:26.119  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes13.dm': No such file or directory
2025-09-01 18:33:26.123  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes15.dm': No such file or directory
2025-09-01 18:33:26.131  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes16.dm': No such file or directory
2025-09-01 18:33:26.136  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-09-01 18:33:26.147  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-09-01 18:33:26.152  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes6.dm': No such file or directory
2025-09-01 18:33:26.164  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes7.dm': No such file or directory
2025-09-01 18:33:26.167  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
2025-09-01 18:33:26.169  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes9.dm': No such file or directory
2025-09-01 18:33:26.172  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.dm': No such file or directory
2025-09-01 18:33:26.172  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.dm': No such file or directory
2025-09-01 18:33:26.650  8985-8985  nativeloader            com.example.repairorderapp           D  Configuring clns-7 for other apk /data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/lib/x86_64:/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.example.repairorderapp
2025-09-01 18:33:26.679  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Currently set values for:
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V    angle_gl_driver_selection_pkgs=[]
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V    angle_gl_driver_selection_values=[]
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Neither updatable production driver nor prerelease driver is supported.
2025-09-01 18:33:26.685  8985-8985  ActivityThread          com.example.repairorderapp           W  Application com.example.repairorderapp is suspending. Debugger needs to resume to continue.
2025-09-01 18:33:26.686  8985-8985  System.out              com.example.repairorderapp           I  Sending WAIT chunk
2025-09-01 18:33:26.686  8985-8985  System.out              com.example.repairorderapp           I  Waiting for debugger first packet
2025-09-01 18:33:26.805  8985-8988  nativeloader            com.example.repairorderapp           D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-09-01 18:33:26.988  8985-8985  System.out              com.example.repairorderapp           I  Debug.suspendAllAndSentVmStart
2025-09-01 18:33:27.396  8985-8985  System.out              com.example.repairorderapp           I  Debug.suspendAllAndSendVmStart, resumed
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM with version 2.1.0 has multidex support
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  Installing application
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM has multidex support, MultiDex support library is disabled.
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  Installing application
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM has multidex support, MultiDex support library is disabled.
2025-09-01 18:33:27.612  8985-8985  WM-WrkMgrInitializer    com.example.repairorderapp           D  Initializing WorkManager with default configuration.
2025-09-01 18:33:27.731  8985-8985  WM-PackageManagerHelper com.example.repairorderapp           D  Skipping component enablement for androidx.work.impl.background.systemjob.SystemJobService
2025-09-01 18:33:27.731  8985-8985  WM-Schedulers           com.example.repairorderapp           D  Created SystemJobScheduler and enabled SystemJobService
2025-09-01 18:33:27.842  8985-9005  CompatChangeReporter    com.example.repairorderapp           D  Compat change id reported: 253665015; UID 10228; state: ENABLED
2025-09-01 18:33:28.040  8985-8985  RemoteConfigManager     com.example.repairorderapp           D  API客户端初始化成功（使用Token认证）
2025-09-01 18:33:28.069  8985-8985  RemoteConfigManager     com.example.repairorderapp           I  RemoteConfigManager初始化完成，等待登录成功后更新配置
2025-09-01 18:33:28.083  8985-8985  EnhancedLogCollector    com.example.repairorderapp           I  日志收集器已初始化
2025-09-01 18:33:28.084  8985-8985  EnhancedLogUtils        com.example.repairorderapp           D  增强日志系统已初始化
2025-09-01 18:33:28.096  8985-9009  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:28.096  8985-9009  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:28.111  8985-8985  RepairOrderApp          com.example.repairorderapp           I  增强日志系统初始化成功
2025-09-01 18:33:28.114  8985-9011  RemoteConfigManager     com.example.repairorderapp           I  加载缓存配置成功: 1.0.0
2025-09-01 18:33:28.114  8985-9011  RemoteConfigManager     com.example.repairorderapp           I  🔔 通知配置更新: 上传间隔=300秒
2025-09-01 18:33:28.114  8985-9011  EnhancedLogCollector    com.example.repairorderapp           D  配置已更新: 1.0.0
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           D  设置全局协程异常处理器
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           I  全局异常处理器设置完成
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           I  全局异常处理器已初始化
2025-09-01 18:33:28.124  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局异常处理器初始化成功
2025-09-01 18:33:28.126  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局异常监控服务启动成功
2025-09-01 18:33:28.132  8985-8985  CrashHandler            com.example.repairorderapp           I  全局异常处理器已安装
2025-09-01 18:33:28.132  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局崩溃处理器安装成功
2025-09-01 18:33:28.134  8985-8985  AppLifecycleTracker     com.example.repairorderapp           I  应用生命周期跟踪器已注册
2025-09-01 18:33:28.134  8985-8985  RepairOrderApp          com.example.repairorderapp           I  应用生命周期跟踪器注册成功
2025-09-01 18:33:28.153  8985-8985  LogUploadManager        com.example.repairorderapp           I  🔧 LogUploadManager开始初始化...
2025-09-01 18:33:28.158  8985-8985  LogUploadManager        com.example.repairorderapp           D  API客户端初始化成功（使用Token认证）
2025-09-01 18:33:28.172  8985-8985  LogUploadManager        com.example.repairorderapp           D  日志上传仓库初始化成功
2025-09-01 18:33:28.173  8985-8985  LogUploadManager        com.example.repairorderapp           I  测试模式：使用纯远程配置，上传间隔: 300秒
2025-09-01 18:33:28.173  8985-8985  LogUploadManager        com.example.repairorderapp           I  LogUploadManager 初始化完成，使用简化防重复机制
2025-09-01 18:33:28.174  8985-8985  LogUploadManager        com.example.repairorderapp           I  日志上传管理器已初始化（智能调度模式）
2025-09-01 18:33:28.174  8985-8985  LogUploadManager        com.example.repairorderapp           D  === 运行时配置摘要 ===
构建类型: debug
调试模式: true
版本信息: 1.0.3-debug (1)
🔧 实际运行配置:
日志上传间隔: 300秒
位置日志间隔: 600秒
WorkManager间隔: 1分钟
日志级别: INFO
最大日志文件: 10MB
最大文件数量: 5个
详细日志: true
网络超时: 10秒
配置来源: 默认配置
2025-09-01 18:33:28.175  8985-9012  LogUploadManager        com.example.repairorderapp           I  开始清理历史问题数据...
2025-09-01 18:33:28.175  8985-8985  LogUploadManager        com.example.repairorderapp           I  启动定时上传任务，间隔: 300秒
2025-09-01 18:33:28.175  8985-8985  LogUploadManager        com.example.repairorderapp           I  🚀 定时上传任务已启动，上传间隔: 300秒
2025-09-01 18:33:28.176  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  设备数据上传管理器已初始化
2025-09-01 18:33:28.176  8985-8985  RepairOrderApp          com.example.repairorderapp           I  设备数据上传管理器初始化成功
2025-09-01 18:33:28.176  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:28.178  8985-8985  TokenFixer              com.example.repairorderapp           D  开始检查令牌状态...
2025-09-01 18:33:28.179  8985-8985  TokenFixer              com.example.repairorderapp           D  令牌有效
2025-09-01 18:33:28.179  8985-9011  LogUploadManager        com.example.repairorderapp           I  🎯 执行首次立即上传...
2025-09-01 18:33:28.179  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=********, userName=苏应来
2025-09-01 18:33:28.179  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:28.180  8985-8985  RepairOrderApp          com.example.repairorderapp           I  TencentMap SDK 隐私协议已同意
2025-09-01 18:33:28.180  8985-9011  LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-09-01 18:33:28.182  8985-9016  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:28.183  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-09-01 18:33:28.185  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-09-01 18:33:28.196  8985-8985  BootCompletedReceiver   com.example.repairorderapp           D  BOOT_COMPLETED received, scheduling location worker.
2025-09-01 18:33:28.206  8985-9012  LogUploadManager        com.example.repairorderapp           D  没有发现需要清理的历史数据
2025-09-01 18:33:28.211  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-09-01 18:33:28.211  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 6
2025-09-01 18:33:28.213  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:33:28.213  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:33:28.237  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:28.240  8985-8985  Choreographer           com.example.repairorderapp           I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-09-01 18:33:28.257  8985-9009  DeviceInfoCollector     com.example.repairorderapp           I  设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:28.294  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:28.300  8985-9016  DeviceDataUploadManager com.example.repairorderapp           D  设备信息无变化，跳过上传
2025-09-01 18:33:28.305  8985-9016  DeviceDataUploadManager com.example.repairorderapp           D  没有待上传的崩溃信息
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 3497
2025-09-01 18:33:28.332  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722109853,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2721,\"memoryUsage\":14321344}","id":1521,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2721ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:21:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722169856,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2713,\"memoryUsage\":15249760}","id":1523,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2713ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:22:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722229918,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2757,\"memoryUsage\":15678256}","id":1525,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2757ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:23:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722289889,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2716,\"memoryUsage\":16196864}","id":1527,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2716ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:24:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722349906,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2719,\"memoryUsage\":8546960}","id":1529,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2719ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:25:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722409927,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2726,\"memoryUsage\":9225312}","id":1531,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2726ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:26:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:33:28.332  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (3497-byte body)
2025-09-01 18:33:28.332  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.332  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.332  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:28.333  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:28.333  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.376  8985-8985  AppCompatDelegate       com.example.repairorderapp           D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-09-01 18:33:28.407  8985-9047  TrafficStats            com.example.repairorderapp           D  tagSocket(112) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:28.430  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: SplashActivity
2025-09-01 18:33:28.504  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (unsupported, reflection, allowed)
2025-09-01 18:33:28.505  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
2025-09-01 18:33:28.509  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 SplashActivity 设置全局触摸监听
2025-09-01 18:33:28.693  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.693  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.694  8985-9047  TokenInterceptor        com.example.repairorderapp           I  Token过期次数: 1/3
2025-09-01 18:33:28.695  8985-8985  RepairOrderApp          com.example.repairorderapp           D  设置启动页面状态: true
2025-09-01 18:33:28.695  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (362ms)
2025-09-01 18:33:28.695  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:47 GMT
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:28.697  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: b655fb92dd974d6fbaccd93690d7797f
2025-09-01 18:33:28.697  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:28.699  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:28.699  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:28.710  8985-8985  SplashActivity          com.example.repairorderapp           D  知识库缓存和状态已在应用启动时清除
2025-09-01 18:33:28.712  8985-9012  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (6条) - 响应: 会话过期
2025-09-01 18:33:28.712  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 5
2025-09-01 18:33:28.713  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:33:28.713  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:33:28.715  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: SplashActivity
2025-09-01 18:33:28.719  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: SplashActivity
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 2767
2025-09-01 18:33:28.727  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722169856,"deviceId":"cf7f6ce27817ef1a","id":1522,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:22:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722229919,"deviceId":"cf7f6ce27817ef1a","id":1524,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:23:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722289889,"deviceId":"cf7f6ce27817ef1a","id":1526,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:24:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722349906,"deviceId":"cf7f6ce27817ef1a","id":1528,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:25:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722409929,"deviceId":"cf7f6ce27817ef1a","id":1530,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:26:49","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:33:28.727  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (2767-byte body)
2025-09-01 18:33:28.728  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:28.731  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.732  8985-8985  HWUI                    com.example.repairorderapp           W  Unknown dataspace 0
2025-09-01 18:33:28.744  8985-8985  GlobalExceptionMonitor  com.example.repairorderapp           I  全局异常监控服务已启动
2025-09-01 18:33:28.746  8985-8985  TokenInterceptor        com.example.repairorderapp           D  获取当前Activity: SplashActivity
2025-09-01 18:33:28.746  8985-8985  TokenInterceptor        com.example.repairorderapp           I  准备显示Token过期对话框 (第1次)，当前Activity: SplashActivity
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload (剩余冷却时间: 9秒)
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (54ms)
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:47 GMT
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: fb6c9e95bb0d4d77af2081cc772929e7
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:28.785  8985-9016  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (5条) - 响应: 会话过期
2025-09-01 18:33:28.785  8985-9016  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-09-01 18:33:28.789  8985-9016  LogUploadManager        com.example.repairorderapp           D  标记6条日志为已上传
2025-09-01 18:33:28.789  8985-9016  LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 6条
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  标记5条日志为已上传
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 5条
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 11条
2025-09-01 18:33:28.790  8985-9016  SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-09-01 18:33:28.801  8985-8989  .repairorderapp         com.example.repairorderapp           I  Compiler allocated 4219KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
2025-09-01 18:33:28.820  8985-8985  TokenInterceptor        com.example.repairorderapp           I  Token过期对话框显示成功 (第1次)
2025-09-01 18:33:28.923  8985-9041  EGL_emulation           com.example.repairorderapp           I  Opening libGLESv1_CM_emulation.so
2025-09-01 18:33:28.923  8985-9041  EGL_emulation           com.example.repairorderapp           I  Opening libGLESv2_emulation.so
2025-09-01 18:33:28.957  8985-9041  HWUI                    com.example.repairorderapp           W  Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
2025-09-01 18:33:28.957  8985-9041  HWUI                    com.example.repairorderapp           W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-09-01 18:33:29.014  8985-9041  Gralloc4                com.example.repairorderapp           I  mapper 4.x is not supported
2025-09-01 18:33:29.161  8985-8989  .repairorderapp         com.example.repairorderapp           I  Compiler allocated 5174KB to compile void android.view.ViewRootImpl.performTraversals()
2025-09-01 18:33:30.155  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=13.21ms min=1.98ms max=36.50ms count=60
2025-09-01 18:33:30.437  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=120.64ms min=12.86ms max=1029.70ms count=10
2025-09-01 18:33:30.719  8985-8985  TokenFixer              com.example.repairorderapp           D  开始检查令牌状态...
2025-09-01 18:33:30.719  8985-8985  TokenFixer              com.example.repairorderapp           D  令牌有效
2025-09-01 18:33:30.723  8985-8985  TokenFixer              com.example.repairorderapp           D  验证令牌有效性: 25b4ac48-5...
2025-09-01 18:33:30.730  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_verifyToken
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 25b4ac48-577e-4008-8fe1-5578b1ccdf0c
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:30.734  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (剩余冷却时间: 7秒)
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (57ms)
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:49 GMT
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 3c2bdb93243047168aabc3002f6824f0
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:30.793  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:30.793  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:30.795  8985-8985  TokenManager            com.example.repairorderapp           W  验证令牌返回非成功状态: 401, 消息:
2025-09-01 18:33:30.795  8985-8985  SplashActivity          com.example.repairorderapp           W  令牌远程验证失败，Token可能已过期，需要重新登录
2025-09-01 18:33:30.795  8985-8985  SplashActivity          com.example.repairorderapp           I  已清除过期Token，跳转到登录页面
2025-09-01 18:33:30.795  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: app_startup - 耗时: 2281ms [耗时: 2281ms] [内存: 11506KB]
2025-09-01 18:33:30.814  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity暂停: SplashActivity
2025-09-01 18:33:30.846  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: LoginActivity
2025-09-01 18:33:30.849  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 LoginActivity 设置全局触摸监听
2025-09-01 18:33:30.953  8985-9066  ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn
2025-09-01 18:33:30.968  8985-9066  TrafficStats            com.example.repairorderapp           D  tagSocket(146) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:30.972  8985-8985  LoginActivity           com.example.repairorderapp           D  开始获取验证码...
2025-09-01 18:33:30.974  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getCaptcha
2025-09-01 18:33:30.976  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-09-01 18:33:30.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-09-01 18:33:30.977  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:30.979  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:30.979  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: LoginActivity
2025-09-01 18:33:30.980  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: LoginActivity
2025-09-01 18:33:31.074  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/captcha (97ms)
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:49 GMT
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:31.076  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"1ae86997-f10d-44d9-8157-cd24a535f214","second":"data:image/gif;base64,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
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Ahg+Ijf67TOeIl5H2SemVCGvGHPHwBCCFMAx7+IKrDzQ5TnSTIoFqox+A5sjO2kEMceAU0ijTPaRGxRW1CCKYHVgQX9hlciYzJwSjhh5MzNLDBFMDjzozkghKjyBAlWigQhN0zO6nqCvAGeNC72M6eDYWL2tRGz4iyZKJXsuhaetLQgAAAIfkEACMAAAAsAAAAAIIAMACHAAAAAQEBAgICAwMDBAQEBQUFBgYGBwcHCAgICQkJCgoKCwsLDAwMDQ0NDg4ODw8PEBAQEREREhISExMTFBQUFRUVFhYWFxcXFhkdFRwjFR4oFCEtEyMzEiU3ESY8EChADylEDitIDSxLDC1OCy5RCi9UCS9WBzBaBTFdBDFfAzJhAjJiATJjATJkATJkADJlADJlADJlADJlADNlADNlADNlADNlATNkATNkAjNkAzNjBDNiBjRhCDRgCzVeDTVdDzZcETZbFDdaFzhZGjlYHTpXITxWJT1VKT9ULkBTMkJSOERRPUdRQklQSUxPT09PUFBQUVFRUlJSU1NTVFRUVVVVVlZWV1dXWFhYWVlZWlpaYFxWZV9Sa2FPcGNLdGVIeWdEfWlBgWo+hWw7iG03i241mXMupngosX0ju4Afw4Qby4cY2JAd4pcj6Z0o76It86Yx9ak196s4+K06+a48+a48+a48+K0696s49ak286Yy76Mu6Z4p4pgl2JEgzIkdxYchvYUntIIuqoA2oH4/lXtKlH1Qk35WkoBdkYJkkIRsj4Zzj4h8jouEjo6Oj4+PkJCQkZGRkpKSk5OTlJSUlZWVlpaWl5eXmJiYmZmZmpqam5ubnJycnZ2dnp6en5+foKCgoaGhoqKio6OjpKSkpaWlpqamp6enqKioqampqqqqq6urrKysra2trq6ur6+vsLCwsbGxs7S1tLe5trq9t7zBub7EusDHusLJu8PLu8TNvMXOvMbPvcbQvcjSztbd3+Tp7O/z9/j5/P39/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+////CP4A/wkcSLCgwYMIEw4UluqMmTOCfimcSLGixYsYM1oUdkZWsGHBaJ1BNEyjyZMoU55MJaugsEG2VMqcSTPlmWAGgZ2pybOnT4NmShYcZuan0YW/cB7NeAZYzp1Lawr7FUuQGTOxomIUNMtgoqxaMwr9N9WV1atXBdEKa/HXGWEE3cJlSzGYLkhIbCUahPbqIEW5lNKtqGgQsGHAEt0cjHDYL1WOtBTq61eRr7mMMdpyeCYW5sz/gq1a9IUM5TOBZv36DLq1yamyDJWhbEaQrKRjXU/MrdCG79/AgwsfbsOgMGC3Djmk/NfXR91iR7EOi5iqoOV9zyjadRh6Slq9wv4G+zWrMO0yYxTReu7dpUlgqX6Ot2WethkvWir54t2eoLAim0xHUSpOqYSYL1Vhh9YZgyTSSBKOzFJgfxMB44gRumTUy1piAYPgdfYNEogstLDiCBCY9CIYhRb5goQWE1IkzCj8uQRMLqwEoiBagihyS1JTmVLEEan8UiOLFQ1TihCRCHgQf+PdoshZtDWommB2VQKEI7XE+I8vYIYpJphIIhSMI0TMEg5FIP2iF1+0nYFGar/UsceE4QAjiyNBbHIZQmMG6kuZCf2yBBMSETTflDtehRortwATzB6fBVOHML6MQoQjrBg5kaBjEppQOKksocgqglCZnSCJ3NZdQf5tKKpLG1t2eVGYok4UDDC7KBIInCEicht7Cr0xFSyOCMGJsRrhqtsgZzRV0HXRCuKhLHs1+hAO3G6hhSRaAMFDDxUJo4sijTjiipF1JCrQeL+4CyiZJ9XhhhteEgTMvXUkhEq0uAiEGC32xRkLdyXloLAwSdiAg8LkJmQXJD9IYtZ+wNRRR27xJvUpvSb9wm9C9rohb0FuVVvwQ4K4YssvOp5xssI5OKIwDg7zYJBjrTgyRCiXheMmIm7E4SlS8R45kLOv8eEGHwIKcy/UEu8YrV/RKkJQtIsRRHMOPnwCDDBaHLGLQJh2IoQjsfyy5kDA/IKKI1w4Aopgcf+Sr/5BYcYNpt7BvE1RyScLVHK/CkFrxnoCUZuyIHBHO4hBNPMw6EC7GIHEiZPosqJ/qDxymTC7OOKU0PF+fhCofztZ0L5uIF5QyXsTdArA73L9D9dK5RLtKZQr7IjAv6yC5hJATOK6QI74ove7w8Nb+OqsO6/6zm/c+3kw976htEApJyKQL1n/o9gZl59fOM27+LJJEI7IUuCZRdSi0BYdrznM8HlfbxDgQgnHrsJ0NIUcbnYj05Xu/qGIaIWnF+XbXbRUlwOcAcESnjvIL5Sghen9wxEdw4mFHBOv5dWlbxSBHRwIEg443Kt2BYGWtGSIk2BU6x86YdBAHIMKIzgMB/7f+8cwVjEER6hOFKpJmiN6kTcYXsRDzquIC/EFt3utsCK3OwMubBitgdDQd2c4RTB6gYktSYhmFjnTEGAhuNA07xd3wQQJPaYSTEVxcAn8xwErEj7yaWcgDURfIq5yohQJBo0X+QUTliAvCzmCS8OYSrzaiBKmKYR7bvCeEJ3mBv8ZhItnON/lvuQQRJBhNvsJXg40MotNCUaSRmriTOzoQYPQDod5rIgMeRcaXTiiDLN5yOQOgsiMCCMSQiiFUDqWt9XMBIq1RNnICIeRQHJtT33yI9eAR0yFoYRsSBiU9Do2k2CgsFxO40Mw0mlClC2nDF3olFCsKTOEFPMkupkwguk6lrpESgozAoSi84JIkMPlUle+BOZDRjk+rnVNlTIRxiaK0Ey33ap61sMI7O5FRYUIDVlC8IQvdrk9rg2zm6ucJSydyLfqORMjw8he94JYxHW9LZAnJYgMuYlSnnSsnf7ZVbz+Brh6cVR2EsvVQKSn1KYmpH9OjSpB5ghUqbbnOPFiqVXbw88SbrWpXdXqV6HDT082NSAAOw=="}}
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (15141-byte body)
2025-09-01 18:33:31.172  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=16.93ms min=3.32ms max=59.56ms count=58
2025-09-01 18:33:31.173  8985-8985  AssistStructure         com.example.repairorderapp           I  Flattened final assist data: 3776 bytes, containing 1 windows, 17 views
2025-09-01 18:33:31.177  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码原始响应长度: 15141
2025-09-01 18:33:31.178  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码token: 1ae86997-f10d-44d9-8157-cd24a535f214
2025-09-01 18:33:31.179  8985-8985  LoginActivity           com.example.repairorderapp           D  Base64字符串前20个字符: R0lGODlhggAwAPcAAAAA
2025-09-01 18:33:31.179  8985-8985  LoginActivity           com.example.repairorderapp           D  完整Base64长度: 15024
2025-09-01 18:33:31.181  8985-8985  LoginActivity           com.example.repairorderapp           D  解码后字节数: 11266
2025-09-01 18:33:31.184  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码图片解码成功: 130x48
2025-09-01 18:33:31.238  8985-9066  ServerTest              com.example.repairorderapp           D  服务器连接测试1结果: 200
2025-09-01 18:33:31.238  8985-9066  ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:31.239  8985-9066  TrafficStats            com.example.repairorderapp           D  tagSocket(136) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:31.438  8985-9066  ServerTest              com.example.repairorderapp           D  服务器连接测试2结果: 200
2025-09-01 18:33:31.723  8985-8985  VRI[SplashActivity]     com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:31.724  8985-8985  VRI[SplashActivity]     com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:31.748  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity停止: SplashActivity
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  设置启动页面状态: false
2025-09-01 18:33:31.751  8985-8985  TokenManager            com.example.repairorderapp           D  会话过期对话框引用为空，已清理
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity销毁: SplashActivity
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  SplashActivity销毁，关闭TokenManager对话框
2025-09-01 18:33:31.751  8985-8985  TokenManager            com.example.repairorderapp           D  会话过期对话框引用为空，已清理
2025-09-01 18:33:31.752  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@23fe54a
2025-09-01 18:33:31.760  8985-8985  WindowManager           com.example.repairorderapp           E  android.view.WindowLeaked: Activity com.example.repairorderapp.ui.launch.SplashActivity has leaked window com.android.internal.policy.DecorView{df406aa V.E...... R.....ID 0,0-1024,504}[SplashActivity] that was originally added here (Ask Gemini)
at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1246)
at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1232)
at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:428)
at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
at android.app.Dialog.show(Dialog.java:352)
at com.example.repairorderapp.network.TokenInterceptor.showTokenExpiredDialog(TokenInterceptor.kt:299)
at com.example.repairorderapp.network.TokenInterceptor.handleTokenExpiredInOkHttp$lambda$2(TokenInterceptor.kt:246)
at com.example.repairorderapp.network.TokenInterceptor.$r8$lambda$E9gcnPczV6BxM7YxsaXthat04hQ(Unknown Source:0)
at com.example.repairorderapp.network.TokenInterceptor$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at android.os.Handler.handleCallback(Handler.java:959)
at android.os.Handler.dispatchMessage(Handler.java:100)
at android.os.Looper.loopOnce(Looper.java:232)
at android.os.Looper.loop(Looper.java:317)
at android.app.ActivityThread.main(ActivityThread.java:8705)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:886)
2025-09-01 18:33:31.762  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@ea86b4c
2025-09-01 18:33:33.659  8985-9081  ProfileInstaller        com.example.repairorderapp           D  Installing profile for com.example.repairorderapp
2025-09-01 18:33:34.395  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:53dfdf2c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-09-01 18:33:34.396  8985-8985  InputMethodManager      com.example.repairorderapp           D  showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{2cf3267 VFED..CL. .F.P..ID 79,1189-665,1333 #7f090233 app:id/et_captcha aid=1073741826} flags=0 reason=SHOW_SOFT_INPUT
2025-09-01 18:33:34.407  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=252.17ms min=13.34ms max=2937.96ms count=13
2025-09-01 18:33:35.153  8985-8985  InsetsController        com.example.repairorderapp           D  show(ime(), fromIme=true)
2025-09-01 18:33:35.163  8985-9087  InteractionJankMonitor  com.example.repairorderapp           W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.example.repairorderapp
2025-09-01 18:33:35.368  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:53dfdf2c: onShown
2025-09-01 18:33:35.902  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=498.06ms min=384.39ms max=612.61ms count=3
2025-09-01 18:33:37.401  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=499.59ms min=498.43ms max=500.49ms count=3
2025-09-01 18:33:37.823  8985-8990  .repairorderapp         com.example.repairorderapp           W  Cleared Reference was only reachable from finalizer (only reported once)
2025-09-01 18:33:37.865  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 7170KB AllocSpace bytes, 37(840KB) LOS objects, 49% free, 5732KB/11MB, paused 729us,6.476ms total 64.151ms
2025-09-01 18:33:38.834  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=477.60ms min=429.24ms max=514.22ms count=3
2025-09-01 18:33:40.217  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=276.56ms min=65.09ms max=499.30ms count=5
2025-09-01 18:33:41.484  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=422.33ms min=267.39ms max=500.27ms count=3
2025-09-01 18:33:42.542  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=352.57ms min=58.70ms max=499.65ms count=3
2025-09-01 18:33:43.023  8985-8985  LoginActivity           com.example.repairorderapp           D  开始获取加密密钥...
2025-09-01 18:33:43.024  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getEncryptionKey
2025-09-01 18:33:43.025  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-09-01 18:33:43.026  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-09-01 18:33:43.026  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/key (59ms)
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:01 GMT
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"453de329-4593-47fc-bcc0-f5df154f7e33","second":"04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b"}}
2025-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (225-byte body)
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  密钥响应: {"code":200,"message":"ok","data":{"first":"453de329-4593-47fc-bcc0-f5df154f7e33","second":"04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b"}}
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  密钥token: 453de329-4593-47fc-bcc0-f5df154f7e33
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  公钥: 04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  开始登录流程...
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  用户名: ********
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码: wry5v
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码token: 1ae86997-f10d-44d9-8157-cd24a535f214
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  密码token: 453de329-4593-47fc-bcc0-f5df154f7e33
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  开始使用SM2加密密码...
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  处理后的公钥长度: 130, 公钥前10个字符: 04801f7b8c
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  加密前数据长度: 10
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  格式化后的公钥前10个字符: 04801f7b8c
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  创建SM2曲线参数
2025-09-01 18:33:43.305  8985-8985  LoginActivity           com.example.repairorderapp           D  解码公钥
2025-09-01 18:33:43.306  8985-8985  LoginActivity           com.example.repairorderapp           D  公钥字节长度: 65
2025-09-01 18:33:43.307  8985-8985  LoginActivity           com.example.repairorderapp           D  创建SM2加密引擎
2025-09-01 18:33:43.320  8985-8985  LoginActivity           com.example.repairorderapp           D  执行SM2加密
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  加密完成，密文长度: 107
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  密码加密完成，加密后长度: 214, 加密后前20个字符: 049e0d9656a61e0719ad
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  密码加密完成
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  发送登录请求...
2025-09-01 18:33:43.374  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_login
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 374
2025-09-01 18:33:43.378  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"password":"049e0d9656a61e0719ad61c24baeab37a72f735f1d78323c287b2e55310c888e8db3f72d2b7cc1b8a2f8730ec21d4f6d5b9e657757daf5a835251bcd56da575acdfdb72f110d66542b965c7a00ab997ad530dc648b1fd6ceb380adbf38d08e4156aba99d4c6cacb22993ec","code":"********","passwordToken":"453de329-4593-47fc-bcc0-f5df154f7e33","captcha":"wry5v","captchaToken":"1ae86997-f10d-44d9-8157-cd24a535f214"}
2025-09-01 18:33:43.386  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (374-byte body)
2025-09-01 18:33:43.387  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.388  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.388  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.406  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 6617KB AllocSpace bytes, 0(0B) LOS objects, 49% free, 6044KB/11MB, paused 1.198ms,7.134ms total 44.384ms
2025-09-01 18:33:43.553  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=14.11ms min=2.15ms max=51.62ms count=55
2025-09-01 18:33:43.572  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/wechat/staff/app/login (185ms)
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"********","name":"苏应来"}}}
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (177-byte body)
2025-09-01 18:33:43.574  8985-8985  LoginActivity           com.example.repairorderapp           D  登录响应: {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"********","name":"苏应来"}}}
2025-09-01 18:33:43.574  8985-8985  LoginActivity           com.example.repairorderapp           D  登录成功
2025-09-01 18:33:43.574  8985-8985  TokenInterceptor        com.example.repairorderapp           D  Token过期状态已重置，开始登录保护期
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  保存令牌信息:
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userId: 1730205532934926338
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userCode: ********
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userName: 苏应来
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - engineerId: 1730205532934926338
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  保存Token信息，不设置本地过期时间
2025-09-01 18:33:43.584  8985-8985  LoginActivity           com.example.repairorderapp           D  服务器响应数据: {"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"********","name":"苏应来"}}
2025-09-01 18:33:43.590  8985-8985  LoginActivity           com.example.repairorderapp           D  令牌保存验证: 成功
2025-09-01 18:33:43.593  8985-8985  LoginActivity           com.example.repairorderapp           D  保存登录凭据 - 用户名: ********, 记住密码: true
2025-09-01 18:33:43.593  8985-8985  LoginActivity           com.example.repairorderapp           D  登录令牌保存成功
2025-09-01 18:33:43.596  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getResources
2025-09-01 18:33:43.597  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.598  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.598  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.600  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.602  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.602  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.603  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.603  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.604  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.604  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.605  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.605  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.613  8985-9016  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getCosBucketInfo
2025-09-01 18:33:43.615  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.618  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.618  8985-9091  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/system/resources (77ms)
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.676  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":[{"label":"工单管理","value":"/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"/disassembly","type":{"value":"menu","label":"菜单"}}]}
2025-09-01 18:33:43.676  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3722-byte body)
2025-09-01 18:33:43.677  8985-8985  LoginActivity           com.example.repairorderapp           D  获取到权限数据: 23 项
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/cos/bucket (66ms)
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"bucket":"sczjzy-1332842668","region":"ap-chengdu","prefix":"prod/"}}
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (104-byte body)
2025-09-01 18:33:43.693  8985-9016  PermissionManager       com.example.repairorderapp           D  开始异步初始化权限...
2025-09-01 18:33:43.693  8985-8985  LoginActivity           com.example.repairorderapp           D  设备信息上传前Token验证: 可用
2025-09-01 18:33:43.693  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=********, userName=苏应来
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:43.696  8985-9016  PermissionManager       com.example.repairorderapp           D  权限初始化完成，共 23 项权限
2025-09-01 18:33:43.794  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:43.794  8985-8985  PermissionManager       com.example.repairorderapp           D  通知权限变更，观察者数量: 0
2025-09-01 18:33:43.803  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-09-01 18:33:43.807  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-09-01 18:33:43.808  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=********, userName=苏应来
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:43.868  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.870  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.874  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:43.875  8985-8985  LoginActivity           com.example.repairorderapp           I  登录成功，远程配置更新已触发
2025-09-01 18:33:43.875  8985-8985  LoginActivity           com.example.repairorderapp           D  权限加载完成，正在跳转到主页
2025-09-01 18:33:43.876  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  🚀 登录成功，开始更新远程配置...
2025-09-01 18:33:43.877  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  🚀 开始请求配置: userId=1730205532934926338, deviceId=cf7f6ce27817ef1a, appVersion=1.0.3-debug, hasToken=true
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='********', userName='苏应来'
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-User-Id: 1730205532934926338
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Device-Id: cf7f6ce27817ef1a
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-App-Version: 1.0.3-debug
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.890  8985-9091  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-User-Id: 1730205532934926338
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Device-Id: cf7f6ce27817ef1a
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-App-Version: 1.0.3-debug
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.894  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Ljava/lang/Void;-><init>()V (unsupported, reflection, allowed)
2025-09-01 18:33:43.898  8985-9012  beacon                  com.example.repairorderapp           I  logAble: false , SDKVersion: 4.2.86.12-external
2025-09-01 18:33:43.900  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.900  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:43.901  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1051
2025-09-01 18:33:43.904  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1556140032,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756722823793}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-09-01 10:33:43","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-09-01 10:33:43","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"GMT","totalMemory":2067398656,"userCode":"********","userId":"1730205532934926338","userName":"苏应来"}
2025-09-01 18:33:43.905  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1051-byte body)
2025-09-01 18:33:43.905  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.905  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           E  BeaconSDK init success! appkey is: 0AND05KOZX0E3L2H, packageName is: com.example.repairorderapp
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.920  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务已创建
2025-09-01 18:33:43.923  8985-8985  LocationUpdateService   com.example.repairorderapp           W  后台位置权限状态变化: false -> true
2025-09-01 18:33:43.925  8985-8985  LocationUpdateService   com.example.repairorderapp           I  权限状态检查 - 后台位置: true, 后台限制: false, 精确位置: true
2025-09-01 18:33:43.927  8985-8985  LocationUpdateService   com.example.repairorderapp           D  屏幕状态监听器注册成功
2025-09-01 18:33:43.928  8985-8985  LocationUpdateService   com.example.repairorderapp           D  检查用户信息: userId=1730205532934926338, userName=苏应来
2025-09-01 18:33:43.930  8985-9116  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/app/ActivityThread;->currentProcessName()Ljava/lang/String; (unsupported, reflection, allowed)
2025-09-01 18:33:43.942  8985-8985  定位调试                    com.example.repairorderapp           I  位置服务 - 成功设置隐私合规接口：已同意
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/config/get (剩余保护时间: 29秒)
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/config/get (57ms)
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:43.951  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 9d4124a209e24147a2e94b4681fc1aa6
2025-09-01 18:33:43.954  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.955  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:43.955  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:43.957  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  ✅ API调用成功: code=200
2025-09-01 18:33:43.958  8985-9016  RemoteConfigManager     com.example.repairorderapp           W  ❌ API响应数据为空: success=false, data=false
2025-09-01 18:33:43.958  8985-9016  RemoteConfigManager     com.example.repairorderapp           W  ⚠️ 登录后配置更新失败，将使用本地缓存配置
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-09-01 18:33:43.973  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (68ms)
2025-09-01 18:33:43.973  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:43.975  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 7b83fbb299144994beb84468a6b36f9e
2025-09-01 18:33:43.975  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:43.976  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:43.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:43.993  8985-9012  RepairOrderApp          com.example.repairorderapp           I  COS SDK 初始化成功: Region=ap-chengdu, Bucket=sczjzy-1332842668
2025-09-01 18:33:44.047  8985-9117  TrafficStats            com.example.repairorderapp           D  tagSocket(186) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.068  8985-8992  System                  com.example.repairorderapp           W  A resource failed to call close.
2025-09-01 18:33:44.112  8985-8985  nativeloader            com.example.repairorderapp           D  Load /data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk!/lib/x86_64/libtencentloc.so using ns clns-7 from class loader (caller=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk): ok
2025-09-01 18:33:44.116  8985-8985  nativeloader            com.example.repairorderapp           D  Load libjnirtk.so using ns clns-7 from class loader (caller=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk): dlopen failed: library "libjnirtk.so" not found
2025-09-01 18:33:44.126  8985-9116  TrafficStats            com.example.repairorderapp           D  tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.128  8985-9115  TrafficStats            com.example.repairorderapp           D  tagSocket(188) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  位置请求配置成功
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  初始化位置服务配置
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  定位服务状态: 可用
2025-09-01 18:33:44.160  8985-8985  LocationUpdateService   com.example.repairorderapp           D  注册网络状态监听
2025-09-01 18:33:44.161  8985-8985  LocationUpdateService   com.example.repairorderapp           D  当前网络状态: 可用
2025-09-01 18:33:44.167  8985-8985  LocationUpdateService   com.example.repairorderapp           D  网络状态监听注册成功
2025-09-01 18:33:44.170  8985-8985  LocationUpdateService   com.example.repairorderapp           D  设备省电模式状态: false
2025-09-01 18:33:44.171  8985-8985  LocationUpdateService   com.example.repairorderapp           I  日志系统初始化成功
2025-09-01 18:33:44.171  8985-9141  LocationUpdateService   com.example.repairorderapp           D  网络连接恢复
2025-09-01 18:33:44.174  8985-9012  LocationUpdateService   com.example.repairorderapp           D  没有缓存的位置数据需要上传
2025-09-01 18:33:44.180  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           D  电池优化状态检查: 应用包名=com.example.repairorderapp, 已忽略电池优化=false
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           W  应用未在电池优化白名单中，可能影响后台定位服务的稳定性
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           W  建议用户在设置中将应用加入电池优化白名单
2025-09-01 18:33:44.197  8985-8985  LocationUpdateService   com.example.repairorderapp           D  已显示电池优化提醒通知
2025-09-01 18:33:44.199  8985-8985  LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-09-01 18:33:44.200  8985-8985  LocationUpdateService   com.example.repairorderapp           D  屏幕状态监听器已注册，跳过重复注册
2025-09-01 18:33:44.200  8985-8985  LocationUpdateService   com.example.repairorderapp           D  网络回调已注册，跳过重复注册
2025-09-01 18:33:44.201  8985-8985  LocationUpdateService   com.example.repairorderapp           D  省电模式监听器注册成功
2025-09-01 18:33:44.202  8985-8985  LocationUpdateService   com.example.repairorderapp           D  位置请求配置已更新: GPS=true, 精度=3, 间隔=300000ms
2025-09-01 18:33:44.205  8985-9016  LocationUpdateService   com.example.repairorderapp           I  权限状态 - 精确位置: true, 粗略位置: true
2025-09-01 18:33:44.205  8985-8985  UploadWorker            com.example.repairorderapp           D  已调度周期性位置上传任务。
2025-09-01 18:33:44.205  8985-9016  LocationUpdateService   com.example.repairorderapp           I  位置请求配置: interval=300000, level=3
2025-09-01 18:33:44.206  8985-9016  LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-09-01 18:33:44.206  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务已启动
2025-09-01 18:33:44.206  8985-9012  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:33:44.207  8985-9012  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 距离上次更新已经过去 1756722824 秒
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='********', userName='苏应来'
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  位置请求正在进行中，跳过重复请求
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 299 秒后进行下次更新
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1051
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1556140032,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756722823873}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-09-01 10:33:43","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-09-01 10:33:43","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"GMT","totalMemory":2067398656,"userCode":"********","userId":"1730205532934926338","userName":"苏应来"}
2025-09-01 18:33:44.211  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1051-byte body)
2025-09-01 18:33:44.211  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity暂停: LoginActivity
2025-09-01 18:33:44.211  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.211  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.218  8985-8985  LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-09-01 18:33:44.219  8985-8985  DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-09-01 18:33:44.239  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: MainActivity
2025-09-01 18:33:44.244  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 MainActivity 设置全局触摸监听
2025-09-01 18:33:44.270  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.271  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.271  8985-9047  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-09-01 18:33:44.272  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (60ms)
2025-09-01 18:33:44.274  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:44.275  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:03 GMT
2025-09-01 18:33:44.275  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:44.276  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:44.276  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 353014795e0f4426bfb20d6b08c75ddd
2025-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:44.315  8985-9120  BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-09-01 18:33:44.368  8985-9016  LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-09-01 18:33:44.372  8985-9011  LocationUpdateService   com.example.repairorderapp           D  没有缓存的位置数据需要上传
2025-09-01 18:33:44.374  8985-9012  LocationUpdateService   com.example.repairorderapp           D  保活任务已取消
2025-09-01 18:33:44.379  8985-9016  UploadWorker            com.example.repairorderapp           D  已调度周期性位置上传任务。
2025-09-01 18:33:44.382  8985-9016  定位调试                    com.example.repairorderapp           I  位置更新服务已启动
2025-09-01 18:33:44.513  8985-8985  PermissionManager       com.example.repairorderapp           D  原始权限JSON数据: [{"label":"工单管理","value":"\/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"\/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"\/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"\/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"\/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"\/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"\/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"\/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"\/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"\/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"\/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"\/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"\/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"\/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"\/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"\/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"\/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"\/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"\/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"\/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"\/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"\/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"\/disassembly","type":{"value":"menu","label":"菜单"}}]
2025-09-01 18:33:44.540  8985-8985  MainActivity            com.example.repairorderapp           D  用户已登录，启动位置服务
2025-09-01 18:33:44.541  8985-8985  MainActivity            com.example.repairorderapp           D  位置服务状态:
位置服务开关: ✓ 启用
服务运行状态: ✓ 运行中
配置文件状态: ✓ 正常
配置项数量: 1
location_service_enabled = true
基础位置权限: ✓
后台位置权限: ✓
系统定位服务: ✓
2025-09-01 18:33:44.541  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-09-01 18:33:44.542  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-09-01 18:33:44.543  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:44.543  8985-8985  MainActivity            com.example.repairorderapp           D  应用启动完成 - 调试模式
2025-09-01 18:33:44.543  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: page_load - 耗时: 294ms [耗时: 294ms] [内存: 9406KB]
2025-09-01 18:33:44.545  8985-8985  MainActivity_onCreate   com.example.repairorderapp           I  内存快照 - 使用: 9MB/192MB (4%) [内存: 9439KB]
2025-09-01 18:33:44.547  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: MainActivity
2025-09-01 18:33:44.625  8985-8985  PermissionManager       com.example.repairorderapp           D  注册权限观察者，当前观察者数量: 1
2025-09-01 18:33:44.625  8985-8985  ProfileFragment         com.example.repairorderapp           D  权限已预加载，直接初始化功能按钮
2025-09-01 18:33:44.664  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-09-01 18:33:44.666  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: MainActivity
2025-09-01 18:33:44.669  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderCount
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 19c9a4e1-9af0-42b6-9024-20fffd99c636
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:44.670  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.678  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=281.13ms min=16.79ms max=1070.67ms count=4
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (68ms)
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:03 GMT
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:44.740  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"pendingOrdersCount":"0","myWorkOrderCount":"0","appealOrderCount":"0"}}
2025-09-01 18:33:44.740  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (107-byte body)
2025-09-01 18:33:44.867  8985-8985  LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-09-01 18:33:44.867  8985-8985  DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-09-01 18:33:44.868  8985-8985  LoginActivity           com.example.repairorderapp           I  登录成功，设备信息上传已触发
2025-09-01 18:33:44.873  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-09-01 18:33:44.894  8985-8985  ProfileFragment         com.example.repairorderapp           D  获取工单数量成功：待接工单=0, 我的工单=0, 申诉单=0
2025-09-01 18:33:44.908  8985-9120  CompatChangeReporter    com.example.repairorderapp           D  Compat change id reported: 247079863; UID 10228; state: ENABLED
2025-09-01 18:33:45.053  8985-8985  InsetsController        com.example.repairorderapp           D  hide(ime(), fromIme=true)
2025-09-01 18:33:45.068  8985-8985  RemoteInpu...ectionImpl com.example.repairorderapp           W  requestCursorUpdates on inactive InputConnection
2025-09-01 18:33:45.069  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:9d4d5339: onCancelled at PHASE_CLIENT_ANIMATION_CANCEL
2025-09-01 18:33:45.071  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:f3654d09: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-09-01 18:33:45.071  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:f3654d09: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-09-01 18:33:45.402  8985-8985  VRI[LoginActivity]      com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:45.422  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity停止: LoginActivity
2025-09-01 18:33:45.423  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity销毁: LoginActivity
2025-09-01 18:33:45.424  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@20080da
2025-09-01 18:33:46.808  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=142.48ms min=3.28ms max=1640.02ms count=13
2025-09-01 18:33:46.825  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-09-01 18:33:47.038  8985-9143  TrafficStats            com.example.repairorderapp           D  tagSocket(155) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:47.543  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-09-01 18:33:47.690  8985-8985  UpdateManager           com.example.repairorderapp           D  开始检查更新...
2025-09-01 18:33:47.693  8985-9016  UpdateRepository        com.example.repairorderapp           D  开始检查更新，当前版本: 1
2025-09-01 18:33:47.694  8985-9016  UpdateRepository        com.example.repairorderapp           D  更新检查参数: versionCode=1, versionName=1.0.3-debug, deviceId=cf7f6ce27817ef1a, userId=1730205532934926338
2025-09-01 18:33:47.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:47.697  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:47.828  8985-8985  LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=404, 原因=ERROR_SERVER_NOT_LOCATION
2025-09-01 18:33:47.829  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 3622ms [耗时: 3622ms] [内存: 11937KB]
2025-09-01 18:33:47.831  8985-8985  LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 404 - ERROR_SERVER_NOT_LOCATION
2025-09-01 18:33:48.334  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=111.16ms min=1.96ms max=1348.55ms count=13
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338 (1638ms)
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:06 GMT
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"hasUpdate":false}}
2025-09-01 18:33:49.337  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (54-byte body)
2025-09-01 18:33:49.339  8985-9016  UpdateRepository        com.example.repairorderapp           D  更新检查成功: hasUpdate=false
2025-09-01 18:33:49.339  8985-9016  UpdateRepository        com.example.repairorderapp           D  当前已是最新版本或无权限更新
2025-09-01 18:33:49.342  8985-8985  UpdateManager           com.example.repairorderapp           D  当前已是最新版本
2025-09-01 18:33:49.342  8985-8985  MainActivity            com.example.repairorderapp           D  当前已是最新版本
2025-09-01 18:33:50.112  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=110.98ms min=14.60ms max=1527.41ms count=16
2025-09-01 18:33:50.118  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@7005ef6
2025-09-01 18:33:50.121  8985-9041  HWUI                    com.example.repairorderapp           D  endAllActiveAnimators on 0x72886ff81420 (RippleDrawable) with handle 0x7286b0473550
2025-09-01 18:33:50.172  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=479.24ms min=1.70ms max=3254.67ms count=7
2025-09-01 18:33:58.090  8985-9016  EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 6条
2025-09-01 18:34:28.745  8985-9009  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:35:28.746  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:36:28.748  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:37:28.749  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:38:28.750  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:38:28.791  8985-9009  LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-09-01 18:38:28.791  8985-9009  SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-09-01 18:38:28.791  8985-9009  SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-09-01 18:38:28.795  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-09-01 18:38:28.795  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 4
2025-09-01 18:38:28.797  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:38:28.797  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:38:28.801  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:28.801  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:38:28.801  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 2266
2025-09-01 18:38:28.801  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722810801,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2281,\"memoryUsage\":11782656}","id":1534,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: app_startup - 耗时: 2281ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:33:30","userCode":"","userId":"","userName":""},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722824544,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":294,\"memoryUsage\":9632048}","id":1536,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: page_load - 耗时: 294ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:33:44","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722824546,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"memoryUsage\":9665664}","id":1537,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"内存快照 - 使用: 9MB/192MB (4%)","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"MainActivity_onCreate","timestamp":"2025-09-01 10:33:44","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722827830,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":3622,\"memoryUsage\":12223872}","id":1538,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 3622ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:33:47","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:38:28.801  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (2266-byte body)
2025-09-01 18:38:28.802  8985-9204  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:38:28.802  8985-9204  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:38:28.803  8985-9204  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:38:28.804  8985-9204  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:38:28.804  8985-9204  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:38:28.804  8985-9204  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:38:28.804  8985-9204  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:38:28.871  8985-9204  TrafficStats            com.example.repairorderapp           D  tagSocket(112) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:38:29.059  8985-9204  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.059  8985-9204  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.060  8985-9204  TokenInterceptor        com.example.repairorderapp           I  Token过期次数: 1/3
2025-09-01 18:38:29.060  8985-8985  TokenInterceptor        com.example.repairorderapp           D  获取当前Activity: MainActivity
2025-09-01 18:38:29.060  8985-8985  TokenInterceptor        com.example.repairorderapp           I  准备显示Token过期对话框 (第1次)，当前Activity: MainActivity
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (258ms)
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:33:47 GMT
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: e27a11e50cc24a0ba5a256f6f0b051e3
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:38:29.060  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:38:29.063  8985-9009  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (4条) - 响应: 会话过期
2025-09-01 18:38:29.063  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 3
2025-09-01 18:38:29.065  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:38:29.065  8985-9009  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:38:29.068  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.069  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:38:29.069  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1600
2025-09-01 18:38:29.069  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722808110,"deviceId":"fallback_1756722808093","id":1533,"isUploaded":false,"level":"DEBUG","logType":"BUSINESS","message":"增强日志系统已初始化","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"15 (API 35)","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"EnhancedLogUtils","timestamp":"2025-09-01 10:33:28","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722824171,"deviceId":"cf7f6ce27817ef1a","id":1535,"isUploaded":false,"level":"INFO","logType":"BUSINESS","message":"位置服务日志系统已初始化","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"LocationUpdateService","timestamp":"2025-09-01 10:33:44","userCode":"********","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722827831,"deviceId":"cf7f6ce27817ef1a","id":1532,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"LocationUpdateService","timestamp":"2025-09-01 10:33:47","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:38:29.070  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1600-byte body)
2025-09-01 18:38:29.070  8985-9204  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.070  8985-9204  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:38:29.071  8985-9204  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:38:29.086  8985-8985  TokenInterceptor        com.example.repairorderapp           I  Token过期对话框显示成功 (第1次)
2025-09-01 18:38:29.148  8985-9204  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.148  8985-9204  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:38:29.148  8985-9204  TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload (剩余冷却时间: 9秒)
2025-09-01 18:38:29.148  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (77ms)
2025-09-01 18:38:29.149  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:38:29.149  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:33:48 GMT
2025-09-01 18:38:29.149  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:38:29.149  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:38:29.150  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 2756fd317a5b4f1492feb862a1703b94
2025-09-01 18:38:29.150  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:38:29.150  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:38:29.151  8985-9204  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:38:29.154  8985-9009  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (3条) - 响应: 会话过期
2025-09-01 18:38:29.154  8985-9009  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-09-01 18:38:29.156  8985-9009  LogUploadManager        com.example.repairorderapp           D  标记4条日志为已上传
2025-09-01 18:38:29.156  8985-9009  LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 4条
2025-09-01 18:38:29.159  8985-9016  LogUploadManager        com.example.repairorderapp           D  标记3条日志为已上传
2025-09-01 18:38:29.159  8985-9016  LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 3条
2025-09-01 18:38:29.160  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 7条
2025-09-01 18:38:29.160  8985-9016  SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-09-01 18:38:29.160  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-09-01 18:38:29.160  8985-9016  LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-09-01 18:38:29.169  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 4999KB AllocSpace bytes, 8(160KB) LOS objects, 49% free, 8456KB/16MB, paused 15.976ms,6.979ms total 65.709ms
2025-09-01 18:38:29.213  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=39862.86ms min=9.80ms max=278944.75ms count=7
2025-09-01 18:38:30.717  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=111.15ms min=1.21ms max=1316.41ms count=13
2025-09-01 18:38:44.207  8985-9016  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:38:44.207  8985-9016  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 0 秒后进行下次更新
2025-09-01 18:38:44.369  8985-9009  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:38:44.369  8985-9009  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 距离上次更新已经过去 300 秒
2025-09-01 18:38:44.369  8985-9009  LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-09-01 18:38:44.377  8985-9120  BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-09-01 18:38:44.377  8985-9009  LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-09-01 18:38:44.377  8985-9009  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:38:44.377  8985-9009  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 299 秒后进行下次更新
2025-09-01 18:38:44.395  8985-9009  LocationUpdateService   com.example.repairorderapp           W  唤醒锁丢失，重新获取
2025-09-01 18:38:44.396  8985-9009  LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-09-01 18:38:46.409  8985-9214  TrafficStats            com.example.repairorderapp           D  tagSocket(209) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:38:47.105  8985-8985  LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=404, 原因=ERROR_SERVER_NOT_LOCATION
2025-09-01 18:38:47.105  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 2736ms [耗时: 2736ms] [内存: 9089KB]
2025-09-01 18:38:47.107  8985-8985  LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 404 - ERROR_SERVER_NOT_LOCATION
2025-09-01 18:38:47.108  8985-8985  LocationUpdateService   com.example.repairorderapp           D  与上次位置距离: 0.0米
2025-09-01 18:38:47.108  8985-8985  LocationUpdateService   com.example.repairorderapp           D  设备可能已静止，开始计时。
2025-09-01 18:38:58.110  8985-9011  EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 1条
2025-09-01 18:39:00.395  8985-9006  CompatChangeReporter    com.example.repairorderapp           D  Compat change id reported: 263076149; UID 10228; state: ENABLED
2025-09-01 18:39:00.403  8985-9011  UploadWorker            com.example.repairorderapp           D  开始执行位置数据上传任务...
2025-09-01 18:39:00.404  8985-9011  UploadWorker            com.example.repairorderapp           D  没有需要上传的位置数据。
2025-09-01 18:39:00.406  8985-9005  WM-WorkerWrapper        com.example.repairorderapp           I  Worker result SUCCESS for Work [ id=80d713fe-f3a7-4457-805c-a3091109ea4a, tags={ com.example.repairorderapp.service.UploadWorker, location_upload_work } ]
2025-09-01 18:39:00.647  8985-9011  LocationUpdateWorker    com.example.repairorderapp           D  开始执行WorkManager位置更新任务
2025-09-01 18:39:00.647  8985-9011  LocationUpdateWorker    com.example.repairorderapp           D  位置服务正在运行，跳过Worker更新
2025-09-01 18:39:00.648  8985-9006  WM-WorkerWrapper        com.example.repairorderapp           I  Worker result SUCCESS for Work [ id=6879631e-25a3-4644-9e42-4f340cc20f2c, tags={ com.example.repairorderapp.service.LocationUpdateWorker, location_update_work } ]
2025-09-01 18:39:28.751  8985-9011  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:40:28.752  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:41:28.752  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:42:28.753  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:42:44.397  8985-9016  LocationUpdateService   com.example.repairorderapp           D  刷新唤醒锁
2025-09-01 18:42:44.400  8985-9016  LocationUpdateService   com.example.repairorderapp           D  已释放唤醒锁
2025-09-01 18:42:44.402  8985-9016  LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-09-01 18:42:44.402  8985-9016  LocationUpdateService   com.example.repairorderapp           D  唤醒锁刷新任务已取消
2025-09-01 18:43:28.755  8985-9011  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:43:29.161  8985-9011  LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-09-01 18:43:29.161  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-09-01 18:43:29.161  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-09-01 18:43:29.171  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-09-01 18:43:29.171  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 1
2025-09-01 18:43:29.172  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:43:29.173  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:43:29.174  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.174  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:43:29.174  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 638
2025-09-01 18:43:29.175  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756723127106,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2736,\"memoryUsage\":9307392}","id":1540,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2736ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:38:47","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:43:29.175  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (638-byte body)
2025-09-01 18:43:29.175  8985-9375  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:43:29.175  8985-9375  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:43:29.176  8985-9375  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:43:29.262  8985-9375  TrafficStats            com.example.repairorderapp           D  tagSocket(112) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:43:29.440  8985-9375  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.440  8985-9375  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.440  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token过期对话框已显示，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.440  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (265ms)
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:38:48 GMT
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: cf572b2004df401dbec36fc09c353f30
2025-09-01 18:43:29.441  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:43:29.442  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:43:29.442  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:43:29.443  8985-9011  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (1条) - 响应: 会话过期
2025-09-01 18:43:29.443  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 1
2025-09-01 18:43:29.444  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:43:29.444  8985-9011  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:43:29.446  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.446  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:43:29.446  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 607
2025-09-01 18:43:29.447  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756723127106,"deviceId":"cf7f6ce27817ef1a","id":1539,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"7275f03b-0728-4625-bcd3-7250bae0a289","tag":"LocationUpdateService","timestamp":"2025-09-01 10:38:47","userCode":"********","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:43:29.447  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (607-byte body)
2025-09-01 18:43:29.447  8985-9375  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:43:29.447  8985-9375  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:43:29.495  8985-9375  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.495  8985-9375  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.495  8985-9375  TokenInterceptor        com.example.repairorderapp           D  Token过期对话框已显示，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (48ms)
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:38:48 GMT
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: c0d72a4a19f74aeb805235aa0a2a5ddc
2025-09-01 18:43:29.495  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:43:29.496  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:43:29.496  8985-9375  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:43:29.497  8985-9011  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (1条) - 响应: 会话过期
2025-09-01 18:43:29.497  8985-9011  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-09-01 18:43:29.498  8985-9011  LogUploadManager        com.example.repairorderapp           D  标记1条日志为已上传
2025-09-01 18:43:29.498  8985-9011  LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 1条
2025-09-01 18:43:29.499  8985-9011  LogUploadManager        com.example.repairorderapp           D  标记1条日志为已上传
2025-09-01 18:43:29.499  8985-9011  LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 1条
2025-09-01 18:43:29.499  8985-9011  LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 2条
2025-09-01 18:43:29.499  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-09-01 18:43:29.499  8985-9011  LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-09-01 18:43:29.499  8985-9011  LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-09-01 18:43:44.371  8985-9016  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:43:44.371  8985-9016  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 距离上次更新已经过去 300 秒
2025-09-01 18:43:44.371  8985-9016  LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-09-01 18:43:44.390  8985-9016  LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-09-01 18:43:44.390  8985-9016  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:43:44.390  8985-9016  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 299 秒后进行下次更新
2025-09-01 18:43:46.401  8985-9120  BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-09-01 18:43:46.458  8985-9384  TrafficStats            com.example.repairorderapp           D  tagSocket(219) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:43:47.285  8985-8985  LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=404, 原因=ERROR_SERVER_NOT_LOCATION
2025-09-01 18:43:47.285  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 2914ms [耗时: 2914ms] [内存: 10388KB]
2025-09-01 18:43:47.288  8985-8985  LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 404 - ERROR_SERVER_NOT_LOCATION
2025-09-01 18:43:47.288  8985-8985  LocationUpdateService   com.example.repairorderapp           D  与上次位置距离: 0.0米
2025-09-01 18:43:58.122  8985-9016  EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 1条
2025-09-01 18:44:28.756  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
2025-09-01 18:45:12.086  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=23609.69ms min=15.82ms max=401101.31ms count=17
2025-09-01 18:45:12.100  8985-8985  TokenInterceptor        com.example.repairorderapp           I  用户选择稍后处理Token过期 (第1次)
2025-09-01 18:45:12.100  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@6fd1052
2025-09-01 18:45:12.117  8985-9041  HWUI                    com.example.repairorderapp           D  endAllActiveAnimators on 0x728870008d90 (RippleDrawable) with handle 0x7286b0483210
2025-09-01 18:45:12.123  8985-8985  TokenInterceptor        com.example.repairorderapp           D  Token过期对话框被关闭
2025-09-01 18:45:12.224  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=80602.23ms min=16.61ms max=402903.75ms count=5
2025-09-01 18:45:13.238  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=87.35ms min=2.96ms max=834.85ms count=11
2025-09-01 18:45:13.319  8985-8985  AutofillManager         com.example.repairorderapp           D  view not autofillable - not passing ime action check
2025-09-01 18:45:13.323  8985-8985  RepairOrders            com.example.repairorderapp           D  onViewCreated: fragment=45131628, isDataLoaded=false
2025-09-01 18:45:13.323  8985-8985  RepairOrders            com.example.repairorderapp           D  是否从工单管理菜单进入: true
2025-09-01 18:45:13.356  8985-8985  RepairOrders            com.example.repairorderapp           D  首次进入页面，加载数据
2025-09-01 18:45:13.356  8985-8985  RepairOrders            com.example.repairorderapp           D  loadWorkOrders: fragment=45131628, isDataLoaded=false
2025-09-01 18:45:13.360  8985-8985  RepairOrders            com.example.repairorderapp           D  没有选中任何筛选项，加载全部工单
2025-09-01 18:45:13.361  8985-8985  RepairOrders            com.example.repairorderapp           D  发送API请求，参数: {"pageNumber":"1","pageSize":"50"}
2025-09-01 18:45:13.369  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderListWithArrayParams
2025-09-01 18:45:13.373  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/work-order-pc/page
2025-09-01 18:45:13.373  8985-8985  RepairOrders            com.example.repairorderapp           D  恢复列表位置: position=0, offset=0
2025-09-01 18:45:13.374  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:45:13.374  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 34
2025-09-01 18:45:13.374  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"pageNumber":"1","pageSize":"50"}
2025-09-01 18:45:13.375  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (34-byte body)
2025-09-01 18:45:13.375  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/work-order-pc/page
2025-09-01 18:45:13.377  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/work-order-pc/page
2025-09-01 18:45:13.377  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:13.377  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:13.377  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:13.378  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:13.378  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:13.378  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:13.379  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:13.379  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:13.379  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:13.379  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:13.379  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:13.381  8985-8985  PermissionManager       com.example.repairorderapp           D  移除权限观察者，当前观察者数量: 1
2025-09-01 18:45:13.408  8985-9406  TrafficStats            com.example.repairorderapp           D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/work-order-pc/page (415ms)
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:32 GMT
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:13.791  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:13.795  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"total":"1947","rows":[{"id":"1962688706003349505","code":"WXGD250902000004","productId":"1730431526011556129","deviceGroupId":"1896493762306199553","customerId":"1896493292883890178","customerStaffId":"1896493292888084482","status":{"value":"pending_orders","label":"待接单"},"isAppeal":false,"currentProcess":"CREATE","errorCode":"0","excDesc":"机器打出来赃，有点点，打封面有重影，字体变粗","excPics":[{"key":"prod/39ccc5c0-879a-11f0-a2f5-a98ed42f2e97","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/39ccc5c0-879a-11f0-a2f5-a98ed42f2e97"},{"key":"prod/3a1e6830-879a-11f0-a2f5-a98ed42f2e97","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3a1e6830-879a-11f0-a2f5-a98ed42f2e97"},{"key":"prod/3a529790-879a-11f0-a2f5-a98ed42f2e97","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3a529790-879a-11f0-a2f5-a98ed42f2e97"}],"expectArriveTime":"2025-09-02 13:27:20","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"330.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":22317,"colorCount":109648,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-02 09:27:07","updatedAt":"2025-09-02 09:27:07","deleted":0,"serType":{"label":"散修","value":"SCATTERED"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C5200S","customerName":"枫创图文-灵龙东路","subbranch":"灵龙东路","phone":"18208157015","customerSeq":"KH250303000002","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.651562","longitude":"104.200622"}},{"id":"1962677127719432194","code":"WXGD250902000001","productId":"1730431526003167385","machineNum":"MC2505230003","deviceGroupId":"1926885807088476162","customerId":"1000990000","customerStaffId":"1922108442147868673","status":{"value":"engineer_receive","label":"工程师接单"},"isAppeal":false,"currentProcess":"ENGINEER_RECEIVE","errorCode":"0","excDesc":"卡纸","excPics":[{"key":"prod/8067d290-8795-11f0-a3fd-e9829311c2ad","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/8067d290-8795-11f0-a3fd-e9829311c2ad"}],"expectArriveTime":"2025-09-02 12:41:20","orderReceiveTime":"2025-09-02 09:25:48","prospectArriveTime":"2025-09-02 12:41:20","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"300.00","isAssignEngineer":false,"engineerId":{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","isAvailable":true},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-02 08:41:07","updatedAt":"2025-09-02 09:25:48","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"706","label":"6号机"},"productInfo":"理光/Pro 8220","customerName":"三五图文-一环路","subbranch":"一环路","phone":"13982272501","customerSeq":"KH250109000329","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.67986","longitude":"104.04389"}},{"id":"1962468768265285633","code":"WXGD250901000010","productId":"1730431526011556121","machineNum":"MC2501150156","deviceGroupId":"20000000017","customerId":"1000380000","customerStaffId":"1910526561984045058","status":{"value":"engineer_departure","label":"工程师出发"},"isAppeal":false,"currentProcess":"ENGINEER_DEPARTURE","excDesc":"二转烧了","excPics":[{"key":"prod/d2c65850-8721-11f0-988d-8f8950f7f6cf","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/d2c65850-8721-11f0-988d-8f8950f7f6cf"}],"expectArriveTime":"2025-09-02 12:30:20","orderReceiveTime":"2025-09-02 09:24:55","prospectArriveTime":"2025-09
2025-09-01 18:45:13.795  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ntAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"500.00","totalPay":"500.00","isAssignEngineer":false,"engineerId":{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","isAvailable":true},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":34124,"createdAt":"2025-09-01 18:53:10","updatedAt":"2025-09-02 09:25:05","deleted":0,"serType":{"label":"购机半保","value":"BUY_HALF"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C5300S","customerName":"腾宇广告-西科大","subbranch":"西科大","phone":"13558996696","customerSeq":"KH250109000229","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"31.534292","longitude":"104.699603"}},{"id":"1962462956939755522","code":"WXGD250901000009","productId":"1730431526003167380","machineNum":"MC2501150235","deviceGroupId":"1881645648655282178","customerId":"1006010000","customerStaffId":"1006010001","status":{"value":"close","label":"关闭"},"cancelStatus":"CUSTOMER_CANCEL","isAppeal":false,"currentProcess":"CREATE","errorCode":"0","excDesc":"充电架故障","excPics":[{"key":"prod/8fca8bf0-871e-11f0-8a46-73b58fe6398e","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/8fca8bf0-871e-11f0-8a46-73b58fe6398e"}],"expectArriveTime":"2025-09-02 12:30:20","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"280.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"580.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-01 18:30:04","updatedAt":"2025-09-01 18:30:04","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro 8320S","customerName":"馨桃图书-茂书巷","subbranch":"茂书巷","phone":"18284382781","customerSeq":"KH250109000201","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"29.587019","longitude":"103.766948"}},{"id":"1962460275647037442","code":"WXGD250901000008","productId":"1730431526003167421","deviceGroupId":"1962459584702562306","customerId":"1962458810572455938","customerStaffId":"1962458810576650242","status":{"value":"close","label":"关闭"},"cancelStatus":"CUSTOMER_CANCEL","isAppeal":false,"currentProcess":"CREATE","errorCode":"0","excDesc":"提示没粉了，一直在这个界面，换了新粉桶也提示没粉，粉桶好像没有转。卡口早就断了（图2）应该和这个无关。","excPics":[{"key":"prod/db4ff5d0-871c-11f0-82cc-69a658923a45","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/db4ff5d0-871c-11f0-82cc-69a658923a45"},{"key":"prod/1376cec0-871d-11f0-82cc-69a658923a45","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/1376cec0-871d-11f0-82cc-69a658923a45"}],"expectArriveTime":"2025-09-02 12:30:20","repairPay":"200.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"280.00","totalPay":"280.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":2450000,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-01 18:19:25","updatedAt":"2025-09-01 18:19:25","deleted":0,"serType":{"label":"散修","value":"SCATTERED"},"deviceGroup":{"value":"706","label":"6号机"},"productInfo":"理光/MP 7503","customerName":"东盛广告-华阳大道店","subbranch":"华阳大道店","phone":"18628248883","customerSeq":"KH250901000001","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.511692","longitude":"104.049225"}},{"id":"1962440991772360706","code":"WXGD250901000007","productId":"17304315
2025-09-01 18:45:13.795  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  xcDesc":"打印有花的","excPics":[{"key":"prod/69d233f0-8712-11f0-ac33-ad02fc5746c2","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/69d233f0-8712-11f0-ac33-ad02fc5746c2"}],"expectArriveTime":"2025-09-02 11:32:20","orderReceiveTime":"2025-09-01 17:04:28","prospectArriveTime":"2025-09-02 11:32:20","departureTime":"2025-09-01 17:04:34","actualArriveTime":"2025-09-01 17:13:38","sendReportTime":"2025-09-01 17:50:19","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","isAvailable":true},"isEvaluated":false,"blackWhiteCount":7278425,"colorCount":0,"fiveColourCount":0,"printCount":610637,"createdAt":"2025-09-01 17:02:47","updatedAt":"2025-09-01 17:50:19","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro 8100S","customerName":"视崛图文-一环路","subbranch":"一环路","phone":"18160153797","customerSeq":"KH250109000328","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.673609","longitude":"104.039684"}},{"id":"1962436953295142913","code":"WXGD250901000006","productId":"1730431526003167397","machineNum":"MC2501150193","deviceGroupId":"1890049256488701953","customerId":"1000570000","customerStaffId":"1902907067237236738","status":{"value":"engineer_departure","label":"工程师出发"},"isAppeal":false,"currentProcess":"ENGINEER_DEPARTURE","errorCode":"0","excDesc":"一直出代码","excPics":[{"key":"prod/14522680-8710-11f0-89e2-dd90053a941a","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/14522680-8710-11f0-89e2-dd90053a941a"}],"expectArriveTime":"2025-09-02 11:16:20","orderReceiveTime":"2025-09-02 09:29:00","prospectArriveTime":"2025-09-02 11:16:20","departureTime":"2025-09-02 09:29:25","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"330.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-01 16:46:45","updatedAt":"2025-09-02 09:29:25","deleted":0,"serType":{"label":"普通全保","value":"ALL"},"deviceGroup":{"value":"703","label":"3号机"},"productInfo":"理光/Pro 8110","customerName":"嘉利图文-犀浦下街","subbranch":"犀浦下街","phone":"17302852959","customerSeq":"KH250109000315","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.754896","longitude":"103.9701"}},{"id":"1962436639523454977","code":"WXGD250901000005","productId":"1730431526003167399","machineNum":"MC2501150032","deviceGroupId":"1881666152724295682","customerId":"1881661470836649985","customerStaffId":"1901918674521010177","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"J003","excDesc":"卡纸，打不了双面","excPics":[{"key":"prod/fca5d130-870f-11f0-9ff4-bbc2269e0932","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/fca5d130-870f-11f0-9ff4-bbc2269e0932"},{"key":"prod/0144adb0-8710-11f0-9ff4-bbc2269e0932","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/0144adb0-8710-11f0-9ff4-bbc2269e0932"}],"expectArriveTime":"2025-09-02 11:15:20","orderReceiveTime":"2025-09-01 17:53:27","prospectArriveTime":"2025-09-02 11:15:20","departureTime":"2025-09-01 17:53:37","actualArriveTime":"2025-09-01 18:34:35","sendReportTime":"2025-09-01 19:15:55","repairPay":"250.00","replacePay":"0
2025-09-01 18:45:13.795  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  Pay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","isAvailable":true},"isEvaluated":false,"blackWhiteCount":4806783,"colorCount":0,"fiveColourCount":0,"printCount":69405,"createdAt":"2025-09-01 16:45:30","updatedAt":"2025-09-01 19:15:55","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"704","label":"4号机"},"productInfo":"理光/Pro 8120","customerName":"广州智印-金宇大厦","phone":"17761212885","customerSeq":"KH250121000004","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682451","longitude":"104.084481"}},{"id":"1962426750151540737","code":"WXGD250901000004","productId":"1730431526011556129","machineNum":"MC2504020001","deviceGroupId":"1907352676530663425","customerId":"1004820000","customerStaffId":"1907316514193682433","status":{"value":"pending_orders","label":"待接单"},"isAppeal":false,"currentProcess":"CREATE","excDesc":"打印不了","excPics":[{"key":"prod/836480f0-870a-11f0-a82f-394982198744","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/836480f0-870a-11f0-a82f-394982198744"}],"expectArriveTime":"2025-09-02 10:36:20","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-861978,"createdAt":"2025-09-01 16:06:12","updatedAt":"2025-09-01 16:06:12","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"703","label":"3号机"},"productInfo":"理光/Pro C5200S","customerName":"回形针-易龙物流园","subbranch":"易龙物流园","phone":"17761212885","customerSeq":"KH250109000337","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.527966","longitude":"104.177178"}},{"id":"1962404386101641218","code":"WXGD250901000003","productId":"1730431526003167527","deviceGroupId":"1946030827020263425","customerId":"1003710000","customerStaffId":"1003710001","status":{"value":"pending_orders","label":"待接单"},"isAppeal":false,"currentProcess":"CREATE","errorCode":"0","excDesc":"显示没有黄色粉，新上的粉已经漏完了","excPics":[{"key":"prod/fe8f7df0-86fd-11f0-9324-d7d30724b0ff","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/fe8f7df0-86fd-11f0-9324-d7d30724b0ff"},{"key":"prod/0ec370f0-86fe-11f0-9324-d7d30724b0ff","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/0ec370f0-86fe-11f0-9324-d7d30724b0ff"}],"expectArriveTime":"2025-09-02 09:07:20","repairPay":"170.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"400.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"620.00","totalPay":"620.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"fiveColourCount":0,"printCount":0,"createdAt":"2025-09-01 14:37:20","updatedAt":"2025-09-01 14:37:20","deleted":0,"serType":{"label":"散修","value":"SCATTERED"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP C5503","customerName":"鸿达广告-滨河北路中段","subbranch":"滨河北路中段","phone":"15182919866","customerSeq":"KH250109000065","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"31.08","longitude":"106.57186"}},{"id":"1962401461937750017","code":"WXGD250901000002","productId":"1730431526011556121","machineNum":"MC2501150154","deviceGroupId":"1881683584587264001","customerId":"1000170101","customerStaffId":"1894267310286315521","status":{"value":"engineer_departure","label":"工程师出?
2025-09-01 18:45:13.795  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  oud.com/prod/75d5c5b0-86fc-11f0-b1b1-113554bffeed"}],"expectArriveTime":"2025-09-02 08:55:20","orderReceiveTime":"2025-09-02 09:13:43","prospectArriveTime":"2025-09-02 08:55:20","departureTime":"2025-09-02 09:23:20","repairPay":"250.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"50.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"350.00","totalPay":"350.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":false,"blackWhiteCount":4050820,"colorCount":0,"fiveColourCount":0,"printCount":4045907,"createdAt":"2025-09-01 14:25:43","updatedAt":"2025-09-02 09:23:20","deleted":0,"serType":{"label":"购机半保","value":"BUY_HALF"},"deviceGroup":{"value":"704","label":"4号机"},"productInfo":"理光/Pro C5300S","customerName":"邑展图文-大邑","subbranch":"大邑","phone":"19302829238","customerSeq":"KH250109000089","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.569363","longitude":"103.517806"}},{"id":"1962308286405525505","code":"WXGD250901000001","productId":"1877611669350264833","machineNum":"MC2504270004","deviceGroupId":"1919661877386858498","customerId":"1919639242800156674","customerStaffId":"1919665265679847426","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"服务器不能校色","excPics":[{"key":"prod/c07e9df0-86c8-11f0-8a3d-e1a85e32e143","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/c07e9df0-86c8-11f0-8a3d-e1a85e32e143"},{"key":"prod/c0bc4330-86c8-11f0-8a3d-e1a85e32e143","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/c0bc4330-86c8-11f0-8a3d-e1a85e32e143"}],"expectArriveTime":"2025-09-01 12:30:20","orderReceiveTime":"2025-09-01 09:04:54","prospectArriveTime":"2025-09-01 12:30:20","departureTime":"2025-09-01 09:28:46","actualArriveTime":"2025-09-01 10:22:54","sendReportTime":"2025-09-01 12:06:40","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":false,"blackWhiteCount":9943,"colorCount":370767,"fiveColourCount":0,"printCount":19572,"createdAt":"2025-09-01 08:15:28","updatedAt":"2025-09-01 12:06:40","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C7500","customerName":"大旗图文-太平南新街","subbranch":"太平南新街","phone":"13882543887","customerSeq":"KH250506000001","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.637885","longitude":"104.088487"}},{"id":"1962111792079552514","code":"WXGD250831000009","productId":"1730431526003167598","machineNum":"MC2503250037","deviceGroupId":"1920027606430830594","customerId":"1894607369652936706","customerStaffId":"1917463642744344577","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"0","excDesc":"带子烂了","excPics":[{"key":"prod/a701bbe0-865b-11f0-8baf-496e74746948","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/a701bbe0-865b-11f0-8baf-496e74746948"}],"expectArriveTime":"2025-09-01 12:30:20","orderReceiveTime":"2025-08-31 20:02:50","prospectArriveTime":"2025-09-01 12:30:20","departureTime":"2025-08-31 20:03:02","actualArriveTime":"2025-08-31 20:50:44","sendReportTime":"2025-09-01 11:19:27","repairPay":"450.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"2
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-6744653,"createdAt":"2025-08-31 19:14:40","updatedAt":"2025-09-01 11:19:27","deleted":0,"serType":{"label":"租赁半保","value":"RENT_HALF"},"deviceGroup":{"value":"714","label":"14号机"},"productInfo":"理光/Pro C9100","customerName":"通艺快印（青于蓝）-花牌坊","subbranch":"花牌坊","phone":"19983654919","customerSeq":"KH250226000003","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682351","longitude":"104.049638"}},{"id":"1962099320123670529","code":"WXGD250831000008","productId":"1730431526011556129","machineNum":"MC2503270011","deviceGroupId":"1881634440480944130","customerId":"1006020000","customerStaffId":"1915936889181892610","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"有黑线","excPics":[{"key":"prod/c0520160-8654-11f0-8ebb-55d74f8854ef","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/c0520160-8654-11f0-8ebb-55d74f8854ef"}],"expectArriveTime":"2025-09-01 12:30:20","orderReceiveTime":"2025-09-01 09:19:15","prospectArriveTime":"2025-09-01 12:30:20","departureTime":"2025-09-01 09:19:30","actualArriveTime":"2025-09-01 09:30:22","sendReportTime":"2025-09-01 10:12:49","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"1048.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"778.00","totalAmount":"1048.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":46085,"colorCount":465416,"fiveColourCount":0,"printCount":-18910,"createdAt":"2025-08-31 18:25:07","updatedAt":"2025-09-01 10:12:49","deleted":0,"serType":{"label":"租赁半保","value":"RENT_HALF"},"deviceGroup":{"value":"703","label":"3号机"},"productInfo":"理光/Pro C5200S","customerName":"微熊文化-华翰路","subbranch":"华翰路","phone":"17773841188","customerSeq":"KH250109000135","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.690765","longitude":"104.179129"}},{"id":"1962092265644081154","code":"WXGD250831000007","productId":"1877611669350264833","machineNum":"MC2501150011","deviceGroupId":"1881677915549200385","customerId":"1004860000","customerStaffId":"1888542642166427650","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"更换清洁纸","excPics":[{"key":"prod/d7327800-8650-11f0-b40e-e5b1117d136e","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/d7327800-8650-11f0-b40e-e5b1117d136e"}],"expectArriveTime":"2025-09-01 12:27:20","orderReceiveTime":"2025-08-31 17:57:27","prospectArriveTime":"2025-09-01 12:27:20","departureTime":"2025-08-31 17:57:34","actualArriveTime":"2025-08-31 17:57:35","sendReportTime":"2025-08-31 18:53:39","confirmReportTime":"2025-08-31 18:55:24","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"2306.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"1976.00","totalAmount":"2306.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","isAvailable":true},"isEvaluated":false,"blackWhiteCount":122576,"colorCount":1601251,"fiveColourCount":0,"printCount":30455,"completedAt":"2025-08-31 18:55:24","createdAt":"2025-08-31 17:57:05","updatedAt":"2025-08-31 18:55:24","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.823458","longitude":"104.228127"}},{"id":"1962057112230150145","code":"WXGD250831000006","productId":"1730431526011556129","machineNum":"MC2501150071","deviceGroupId":"1881587220985540609","customerId":"1004820000","customerStaffId":"1907316514193682433","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"J099","excDesc":"卡纸","excPics":[{"key":"prod/4e95c2d0-863d-11f0-b9b3-b3f93e151a17","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/4e95c2d0-863d-11f0-b9b3-b3f93e151a17"}],"expectArriveTime":"2025-09-01 10:07:20","orderReceiveTime":"2025-09-01 09:45:23","prospectArriveTime":"2025-09-01 10:07:20","departureTime":"2025-09-01 09:51:21","actualArriveTime":"2025-09-01 10:59:36","sendReportTime":"2025-09-01 13:25:44","confirmReportTime":"2025-09-01 16:04:09","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"330.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":false,"blackWhiteCount":39450,"colorCount":1508799,"fiveColourCount":0,"printCount":865532,"completedAt":"2025-09-01 16:04:09","createdAt":"2025-08-31 15:37:23","updatedAt":"2025-09-01 16:04:09","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C5200S","customerName":"回形针-易龙物流园","subbranch":"易龙物流园","phone":"17761212885","customerSeq":"KH250109000337","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.527966","longitude":"104.177178"}},{"id":"1962004924527132674","code":"WXGD250831000005","productId":"1730431526003167421","machineNum":"MC2501150207","deviceGroupId":"1899355050195619841","customerId":"1000740000","customerStaffId":"1000740001","status":{"value":"close","label":"关闭"},"cancelStatus":"CUSTOMER_CANCEL","isAppeal":false,"currentProcess":"CREATE","errorCode":"sc555_00","excDesc":"这个代码以前都可以806182清除，现在平凡冒出来，清除了一会又有","excPics":[{"key":"prod/2d020d30-8620-11f0-bbc2-01eefe3c7470","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/2d020d30-8620-11f0-bbc2-01eefe3c7470"}],"expectArriveTime":"2025-08-31 16:09:20","repairPay":"200.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"280.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-17848,"createdAt":"2025-08-31 12:10:01","updatedAt":"2025-08-31 12:10:01","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP 7503","customerName":"老实人-川音三巷","subbranch":"川音三巷","phone":"13330949079","customerSeq":"KH250109000077","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.802646","longitude":"104.180002"}},{"id":"1962001208910659585","code":"WXGD250831000004","productId":"1730431526003167387","machineNum":"MC2501150089","deviceGroupId":"1889958805865848833","customerId":"1001350000","customerStaffId":"1888468717835251713","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"卡纸","excPics":[{"key":"prod/4b89fa80-861e-11f0-a7bc-355815a6ef28","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/4b89fa80-861e-11f0-a7bc-355815a6ef28"}],"expectArriveTime":"2025-08-31 15:55:20","orderReceiveTime":"2025-08-31 16:
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ","confirmReportTime":"2025-08-31 18:23:14","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"330.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":8833379,"colorCount":0,"fiveColourCount":0,"printCount":142016,"completedAt":"2025-08-31 18:23:15","createdAt":"2025-08-31 11:55:15","updatedAt":"2025-08-31 18:23:15","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"706","label":"6号机"},"productInfo":"理光/Pro 8200S","customerName":"蓉艺易印-荆竹园","subbranch":"荆竹园","phone":"18784522582","customerSeq":"KH250109000172","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.804696","longitude":"104.058368"}},{"id":"1961992445264773122","code":"WXGD250831000003","productId":"1730431526011556131","machineNum":"MC2501150029","deviceGroupId":"1960910294054191105","customerId":"1003640000","customerStaffId":"1914994772058148866","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"最底下有点点跟底灰。","excPics":[{"key":"prod/669599b0-8619-11f0-9ba0-413bddcda168","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/669599b0-8619-11f0-9ba0-413bddcda168"}],"expectArriveTime":"2025-08-31 15:20:20","orderReceiveTime":"2025-09-01 09:24:38","prospectArriveTime":"2025-08-31 15:20:20","departureTime":"2025-09-01 09:30:10","actualArriveTime":"2025-09-01 11:46:51","sendReportTime":"2025-09-01 16:41:10","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"1585.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"1255.00","totalAmount":"1585.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","isAvailable":true},"isEvaluated":false,"blackWhiteCount":34090,"colorCount":466649,"fiveColourCount":0,"printCount":0,"createdAt":"2025-08-31 11:20:26","updatedAt":"2025-09-01 16:41:10","deleted":0,"serType":{"label":"购机半保","value":"BUY_HALF"},"deviceGroup":{"value":"707","label":"7号机"},"productInfo":"理光/MP C6503","customerName":"湘禾图文-大连北路","subbranch":"大连北路","phone":"19150185739","customerSeq":"KH250109000087","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.571191","longitude":"104.234425"}},{"id":"1961968783048425474","code":"WXGD250831000002","productId":"1877609581216018434","machineNum":"MC2501150104","deviceGroupId":"1881587724771782658","customerId":"1003830000","customerStaffId":"1890385956608012290","status":{"value":"engineer_arrive","label":"工程师到达"},"isAppeal":false,"currentProcess":"ENGINEER_ARRIVE","excDesc":"卡纸，断墨","excPics":[{"key":"prod/48355670-860c-11f0-9d8b-f920c5213084","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/48355670-860c-11f0-9d8b-f920c5213084"}],"expectArriveTime":"2025-08-31 13:46:20","orderReceiveTime":"2025-09-01 09:45:13","prospectArriveTime":"2025-08-31 13:46:20","departureTime":"2025-09-01 13:25:53","actualArriveTime":"2025-09-01 14:21:49","repairPay":"200.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"280.00","totalPay":"280.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-89266,"creat
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ":{"value":"703","label":"3号机"},"productInfo":"爱普生/M21000A","customerName":"金成图文-宁波路","subbranch":"宁波路","phone":"19381832168","customerSeq":"KH250109000214","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.432822","longitude":"104.080665"}},{"id":"1961886543304048642","code":"WXGD250831000001","productId":"1877611669350264833","deviceGroupId":"1930659319892680706","customerId":"1006240000","customerStaffId":"1930881335463780353","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"补充黑色润滑剂","excPics":[{"key":"prod/9b301960-85de-11f0-9e23-bf01cb7c95e1","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9b301960-85de-11f0-9e23-bf01cb7c95e1"},{"key":"prod/9b945970-85de-11f0-9e23-bf01cb7c95e1","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9b945970-85de-11f0-9e23-bf01cb7c95e1"}],"expectArriveTime":"2025-08-31 12:30:20","orderReceiveTime":"2025-08-31 09:05:41","prospectArriveTime":"2025-08-31 12:30:20","departureTime":"2025-08-31 09:06:10","actualArriveTime":"2025-08-31 09:52:09","sendReportTime":"2025-08-31 10:39:34","confirmReportTime":"2025-08-31 20:00:39","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"330.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":22481,"colorCount":784928,"fiveColourCount":0,"printCount":41743,"completedAt":"2025-08-31 20:00:40","createdAt":"2025-08-31 04:19:37","updatedAt":"2025-08-31 20:00:40","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C7500","customerName":"需求者印务-憩园街","subbranch":"憩园街","phone":"18682556258","customerSeq":"KH250109000022","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.816847","longitude":"104.0739"}},{"id":"1961742428276899841","code":"WXGD250830000003","productId":"1730431526011556131","machineNum":"MC2501220029","deviceGroupId":"1897886550734172162","customerId":"1889590385525665793","customerStaffId":"1889590385529860098","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"有底灰，有杠，在同一个位置","excPics":[{"key":"prod/97b99ea0-858e-11f0-94c3-656f646efe72","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/97b99ea0-858e-11f0-94c3-656f646efe72"},{"key":"prod/9d1625d0-858e-11f0-94c3-656f646efe72","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9d1625d0-858e-11f0-94c3-656f646efe72"}],"expectArriveTime":"2025-08-31 12:30:20","orderReceiveTime":"2025-08-31 08:50:38","prospectArriveTime":"2025-08-31 12:30:20","departureTime":"2025-08-31 15:17:38","actualArriveTime":"2025-08-31 15:56:42","sendReportTime":"2025-08-31 16:54:31","confirmReportTime":"2025-08-31 16:55:06","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":false,"blackWhiteCount":164511,"colorCount":166941,"fiveColourCount":0,"printCount":19715,"completedAt":"2025-08-31 16:55:06","createdAt":"2025-08-30 18:46:57","updatedAt":"2025-08-31 16:55:06","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"705","label":"5号机"},"productInfo":"理光/MP C6503","customerName":
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  l":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.612354","longitude":"104.125548"}},{"id":"1961687492390404098","code":"WXGD250830000002","productId":"1877611669350264833","machineNum":"MC2504270002","deviceGroupId":"1917238444245962754","customerId":"1894607369652936706","customerStaffId":"1953734397630894081","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"打印出来有细小的点点","excPics":[{"key":"prod/23ff4a50-8570-11f0-8bac-e932b8cd51c5","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/23ff4a50-8570-11f0-8bac-e932b8cd51c5"}],"expectArriveTime":"2025-08-31 09:38:20","orderReceiveTime":"2025-08-30 15:08:55","prospectArriveTime":"2025-08-31 09:38:20","departureTime":"2025-08-30 15:09:02","actualArriveTime":"2025-08-30 15:09:03","sendReportTime":"2025-08-30 15:22:34","confirmReportTime":"2025-08-30 15:27:58","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"1600.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"1300.00","totalAmount":"1600.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":4123,"colorCount":784927,"fiveColourCount":0,"printCount":39352,"completedAt":"2025-08-30 15:27:58","createdAt":"2025-08-30 15:08:39","updatedAt":"2025-08-30 15:27:58","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"711","label":"11号机"},"productInfo":"理光/Pro C7500","customerName":"通艺快印（青于蓝）-花牌坊","subbranch":"花牌坊","phone":"15760398043","customerSeq":"KH250226000003","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682351","longitude":"104.049638"}},{"id":"1961671658246881281","code":"WXGD250830000001","productId":"1730431526003167385","machineNum":"MC2505230003","deviceGroupId":"1926885807088476162","customerId":"1000990000","customerStaffId":"1922108442147868673","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"0","excDesc":"j034","excPics":[{"key":"prod/59667d20-8567-11f0-8a71-b9a38e4a1aa3","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/59667d20-8567-11f0-8a71-b9a38e4a1aa3"}],"expectArriveTime":"2025-08-31 08:35:20","orderReceiveTime":"2025-08-30 14:57:02","prospectArriveTime":"2025-08-31 08:35:20","departureTime":"2025-08-30 14:57:49","actualArriveTime":"2025-08-30 15:12:25","sendReportTime":"2025-08-30 18:45:30","confirmReportTime":"2025-08-31 18:50:06","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":true,"blackWhiteCount":1600536,"colorCount":0,"fiveColourCount":0,"printCount":253725,"completedAt":"2025-08-31 18:50:06","createdAt":"2025-08-30 14:05:44","updatedAt":"2025-08-30 18:45:30","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"706","label":"6号机"},"productInfo":"理光/Pro 8220","customerName":"三五图文-一环路","subbranch":"一环路","phone":"13982272501","customerSeq":"KH250109000329","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.67986","longitude":"104.04389"}},{"id":"1961421144208818177","code":"WXGD250829000016","productId":"1877609581216018434","machineNum":"MC2501150105","deviceGroupId":"1888534355668758530","customerId":"1001580000","customerStaffId":"1001580001","status":{"value":"pending_orders","label":"待接
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ac25-259c609908bb","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/49109360-84dc-11f0-ac25-259c609908bb"}],"expectArriveTime":"2025-08-30 12:30:20","repairPay":"200.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"400.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"650.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-407141,"createdAt":"2025-08-29 21:30:17","updatedAt":"2025-08-29 21:30:17","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"707","label":"7号机"},"productInfo":"爱普生/M21000A","customerName":"广印鸿图-营山","subbranch":"营山","phone":"17313901789","customerSeq":"KH250109000341","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"31.074893","longitude":"106.575315"}},{"id":"1961420583178715137","code":"WXGD250829000015","productId":"1730431526011556131","machineNum":"MC2501150093","deviceGroupId":"1881680169064853505","customerId":"1003470000","customerStaffId":"1915276814670954498","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"1、出现杠，之前师傅一直在未维修，要换材料，出现这个问题","excPics":[{"key":"prod/d6b1b4c0-84db-11f0-9d8d-69131620d1e3","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/d6b1b4c0-84db-11f0-9d8d-69131620d1e3"}],"expectArriveTime":"2025-08-30 12:30:20","orderReceiveTime":"2025-08-30 09:23:18","prospectArriveTime":"2025-08-30 12:30:20","departureTime":"2025-08-30 19:49:59","actualArriveTime":"2025-08-30 19:50:01","sendReportTime":"2025-08-30 19:55:18","confirmReportTime":"2025-08-30 20:16:21","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"50.00","discountAmount":"983.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"633.00","totalAmount":"983.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","isAvailable":true},"isEvaluated":false,"blackWhiteCount":169249,"colorCount":680814,"fiveColourCount":0,"printCount":2291,"completedAt":"2025-08-30 20:16:22","createdAt":"2025-08-29 21:28:03","updatedAt":"2025-08-30 20:16:22","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP C6503","customerName":"印拾光-蜀州北路","subbranch":"蜀州北路","phone":"19113520119","customerSeq":"KH250109000262","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.635949","longitude":"103.669556"}},{"id":"1961420190260510721","code":"WXGD250829000014","productId":"1730431526003167421","deviceGroupId":"1882955101753085953","customerId":"1003470000","customerStaffId":"1915276814670954498","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"1、打印不了，出现问题","excPics":[{"key":"prod/b2888100-84db-11f0-9d8d-69131620d1e3","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/b2888100-84db-11f0-9d8d-69131620d1e3"}],"expectArriveTime":"2025-08-30 12:30:20","orderReceiveTime":"2025-08-30 09:23:02","prospectArriveTime":"2025-08-30 12:30:20","departureTime":"2025-08-30 11:30:06","actualArriveTime":"2025-08-30 11:30:08","sendReportTime":"2025-08-30 11:31:49","confirmReportTime":"2025-08-30 20:16:28","repairPay":"200.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"50.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"194204527899973222
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  :8031,"completedAt":"2025-08-30 20:16:28","createdAt":"2025-08-29 21:26:29","updatedAt":"2025-08-30 20:16:28","deleted":0,"serType":{"label":"普通半保","value":"HALF"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/MP 7503","customerName":"印拾光-蜀州北路","subbranch":"蜀州北路","phone":"19113520119","customerSeq":"KH250109000262","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.635949","longitude":"103.669556"}},{"id":"1961417993904832514","code":"WXGD250829000013","productId":"1877611669350264833","machineNum":"MC2504270001","deviceGroupId":"1922560714665369601","customerId":"1005990000","customerStaffId":"1922562263454064641","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"二转脏","excPics":[{"key":"prod/8ac88760-84da-11f0-9190-6beb35b3fc99","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/8ac88760-84da-11f0-9190-6beb35b3fc99"}],"expectArriveTime":"2025-08-30 12:30:20","orderReceiveTime":"2025-08-30 08:47:34","prospectArriveTime":"2025-08-30 12:30:20","departureTime":"2025-08-30 09:42:44","actualArriveTime":"2025-08-30 10:48:53","sendReportTime":"2025-08-30 10:58:48","confirmReportTime":"2025-08-30 17:58:50","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"330.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":false,"blackWhiteCount":22160,"colorCount":871960,"fiveColourCount":0,"printCount":9439,"completedAt":"2025-08-30 17:58:51","createdAt":"2025-08-29 21:17:46","updatedAt":"2025-08-30 17:58:51","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"709","label":"9号机"},"productInfo":"理光/Pro C7500","customerName":"青于蓝-万兴路","subbranch":"万兴路","phone":"18780152459","customerSeq":"KH250109000296","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.817668","longitude":"104.227264"}},{"id":"1961382994879037441","code":"WXGD250829000012","productId":"1730431526003167598","machineNum":"MC2501150036","deviceGroupId":"1946115939657359362","customerId":"1935154314435633153","customerStaffId":"1946120373368037377","status":{"value":"close","label":"关闭"},"cancelStatus":"CUSTOMER_CANCEL","isAppeal":false,"currentProcess":"CREATE","excDesc":"报代码","excPics":[{"key":"prod/1dfccaf0-84c7-11f0-92de-7faed7dd0ec1","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/1dfccaf0-84c7-11f0-92de-7faed7dd0ec1"}],"expectArriveTime":"2025-08-30 12:30:20","repairPay":"450.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"530.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{},"isEvaluated":false,"blackWhiteCount":1,"colorCount":1,"fiveColourCount":0,"printCount":-2386930,"createdAt":"2025-08-29 18:58:41","updatedAt":"2025-08-29 18:58:41","deleted":0,"serType":{"label":"租赁半保","value":"RENT_HALF"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C9100","customerName":"蓉艺易印-眉山","subbranch":"眉山","phone":"18784522582","customerSeq":"KH250618000001","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.520128","longitude":"104.063436"}},{"id":"1961342053535301633","code":"WXGD250829000011","productId":"1730431526003167598","machineNum":"MC2503250037","deviceGroupId":"1920027606430830594","customerId":"1894607369652936706","customerStaffId":"1953734397630894081","status":{"value":"completed","label":"已完成"},"isAppeal"
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  /sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/62030140-84b0-11f0-8fd9-f7f184977ccb"}],"expectArriveTime":"2025-08-30 10:45:20","orderReceiveTime":"2025-08-29 17:59:59","prospectArriveTime":"2025-08-30 10:45:20","departureTime":"2025-08-29 18:00:06","actualArriveTime":"2025-08-29 19:08:47","sendReportTime":"2025-08-30 14:43:56","confirmReportTime":"2025-08-30 15:07:14","repairPay":"450.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"3250.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"2750.00","totalAmount":"3250.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":273122,"colorCount":6471531,"fiveColourCount":0,"printCount":6926,"completedAt":"2025-08-30 15:07:15","createdAt":"2025-08-29 16:16:00","updatedAt":"2025-08-30 15:07:15","deleted":0,"serType":{"label":"租赁半保","value":"RENT_HALF"},"deviceGroup":{"value":"714","label":"14号机"},"productInfo":"理光/Pro C9100","customerName":"通艺快印（青于蓝）-花牌坊","subbranch":"花牌坊","phone":"15760398043","customerSeq":"KH250226000003","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682351","longitude":"104.049638"}},{"id":"1961335910016598017","code":"WXGD250829000010","productId":"1730431526011556067","machineNum":"MC2501150023","deviceGroupId":"1890051841589252098","customerId":"1000610000","customerStaffId":"1937025291926958081","status":{"value":"engineer_receive","label":"工程师接单"},"isAppeal":false,"currentProcess":"ENGINEER_RECEIVE","excDesc":"打印效果有问题","excPics":[{"key":"prod/f284dbc0-84ac-11f0-8c33-093ff637bc0b","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f284dbc0-84ac-11f0-8c33-093ff637bc0b"},{"key":"prod/fa2a8fa0-84ac-11f0-8c33-093ff637bc0b","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/fa2a8fa0-84ac-11f0-8c33-093ff637bc0b"}],"expectArriveTime":"2025-08-30 10:21:20","orderReceiveTime":"2025-09-02 08:57:31","prospectArriveTime":"2025-08-30 10:21:20","repairPay":"150.00","replacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"230.00","totalPay":"230.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":0,"colorCount":0,"fiveColourCount":0,"printCount":-19022,"createdAt":"2025-08-29 15:51:35","updatedAt":"2025-09-02 08:57:31","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP 6054","customerName":"杰斯顿-蓉台大道","subbranch":"蓉台大道","phone":"17761212885","customerSeq":"KH250109000239","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.666939","longitude":"103.852154"}},{"id":"1961332975375335425","code":"WXGD250829000009","productId":"1730431526003167394","deviceGroupId":"1881669304013942785","customerId":"1001220000","customerStaffId":"1901521182017040386","status":{"value":"to_be_settled","label":"待结算"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"0","excDesc":"打印质量，清晰度不够，","excPics":[{"key":"prod/56e752c0-84ab-11f0-a50c-6bd863f69d19","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/56e752c0-84ab-11f0-a50c-6bd863f69d19"}],"expectArriveTime":"2025-08-30 10:09:20","orderReceiveTime":"2025-08-31 08:49:47","prospectArriveTime":"2025-08-30 10:09:20","departureTime":"2025-08-31 09:39:39","actualArriveTime":"2025-08-31 10:23:19","sendReportTime":"2025-08-31 15:11:40","confirmReportTime":"2025-08-31 15:12:33","repairPay":"250.00","replacePay":"0.00","actualReplac
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  rAdditionalPay":"0.00","itemPay":"606.00","totalAmount":"906.00","totalPay":"906.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":false,"blackWhiteCount":6005786,"colorCount":0,"fiveColourCount":0,"printCount":20654,"createdAt":"2025-08-29 15:39:56","updatedAt":"2025-08-31 15:12:33","deleted":0,"serType":{"label":"散修","value":"SCATTERED"},"deviceGroup":{"value":"703","label":"3号机"},"productInfo":"理光/Pro 8100S","customerName":"童画印务-童子街","subbranch":"童子街","phone":"13408403813","customerSeq":"KH250109000288","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.66715","longitude":"104.075069"}},{"id":"1961327542854074370","code":"WXGD250829000008","productId":"1730431526011556131","machineNum":"MC2501150069","deviceGroupId":"1881634119255977985","customerId":"1006050000","customerStaffId":"1006050001","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"Pcu清洁单元","excPics":[{"key":"prod/4ce2a7a0-84a8-11f0-8e2b-47988a030b39","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/4ce2a7a0-84a8-11f0-8e2b-47988a030b39"}],"expectArriveTime":"2025-08-30 09:48:20","orderReceiveTime":"2025-08-30 08:41:49","prospectArriveTime":"2025-08-30 09:48:20","departureTime":"2025-08-30 08:43:33","actualArriveTime":"2025-08-30 09:48:32","sendReportTime":"2025-08-30 11:26:17","confirmReportTime":"2025-08-30 18:32:24","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"744.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"444.00","totalAmount":"744.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":382347,"colorCount":264440,"fiveColourCount":0,"printCount":14664,"completedAt":"2025-08-30 18:32:24","createdAt":"2025-08-29 15:18:21","updatedAt":"2025-08-30 18:32:24","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP C6503","customerName":"天天图文-杉板桥","subbranch":"杉板桥","phone":"17760945408","customerSeq":"KH250109000250","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.662364","longitude":"104.114285"}},{"id":"1961306421664530434","code":"WXGD250829000007","productId":"1730431526011556129","machineNum":"MC2503270011","deviceGroupId":"1881634440480944130","customerId":"1006020000","customerStaffId":"1922612150602309633","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"0","excDesc":"1","excPics":[{"key":"prod/93e505f0-849c-11f0-bfe5-b94c9f59a25c","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/93e505f0-849c-11f0-bfe5-b94c9f59a25c"}],"expectArriveTime":"2025-08-29 17:54:20","orderReceiveTime":"2025-08-29 15:18:31","prospectArriveTime":"2025-08-29 17:54:20","departureTime":"2025-08-29 15:18:49","actualArriveTime":"2025-08-29 15:45:52","sendReportTime":"2025-08-29 17:25:51","confirmReportTime":"2025-08-30 17:30:05","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"270.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"270.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815143298347010","code":"SCZJZY0026","name":"邱开超","isAvailable":true},"isEvaluated":true,"blackWhiteCount":46071,"colorCount":484340,"fiveColourCount":0,"printCount":34120,"completedAt":"2025-08-30 17:30:06","createdAt":"2025-08-29 13:54:25","updatedAt":"2025-08-29 17:25:51","deleted":0,"serType":{"label":"租赁?
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ,"customerName":"微熊文化-华翰路","subbranch":"华翰路","phone":"18173809863","customerSeq":"KH250109000135","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.690765","longitude":"104.179129"}},{"id":"1961273120681738241","code":"WXGD250829000006","productId":"1730431526011556067","machineNum":"MC2501150015","deviceGroupId":"1888911612245467138","customerId":"1888881942942736385","customerStaffId":"1910498846409089025","status":{"value":"wait_confirmed_report","label":"待确认维修报告"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"打印有线","excPics":[{"key":"prod/0d49a990-848a-11f0-b1a1-cdfe02acc4ab","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/0d49a990-848a-11f0-b1a1-cdfe02acc4ab"}],"expectArriveTime":"2025-09-01 09:00:20","orderReceiveTime":"2025-09-01 10:13:39","prospectArriveTime":"2025-09-01 09:00:20","departureTime":"2025-09-01 10:13:49","actualArriveTime":"2025-09-01 12:14:21","sendReportTime":"2025-09-01 16:38:29","repairPay":"150.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"395.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"195.00","totalAmount":"395.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":965190,"colorCount":0,"fiveColourCount":0,"printCount":1949,"createdAt":"2025-08-29 11:42:05","updatedAt":"2025-09-01 16:38:29","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/MP 6054","customerName":"国药-科园南路（创鑫伟业）","phone":"17761212885","customerSeq":"KH250210000008","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.610328","longitude":"104.029929"}},{"id":"1961258101000749057","code":"WXGD250829000005","productId":"1730431526003167394","deviceGroupId":"1889488264532865025","customerId":"1001060000","customerStaffId":"1001060001","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"打印花，有黑线，满色块也很花","excPics":[{"key":"prod/c763cee0-8481-11f0-9ea0-f9d74a5a5d71","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/c763cee0-8481-11f0-9ea0-f9d74a5a5d71"}],"expectArriveTime":"2025-08-29 14:42:20","orderReceiveTime":"2025-08-30 08:41:15","prospectArriveTime":"2025-08-29 14:42:20","departureTime":"2025-08-30 11:27:55","actualArriveTime":"2025-08-30 13:14:11","sendReportTime":"2025-09-01 08:46:42","confirmReportTime":"2025-09-01 17:02:14","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"410.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"110.00","totalAmount":"410.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":6667788,"colorCount":0,"fiveColourCount":0,"printCount":-419980,"completedAt":"2025-09-01 17:02:15","createdAt":"2025-08-29 10:42:24","updatedAt":"2025-09-01 17:02:15","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro 8100S","customerName":"视崛图文-一环路","subbranch":"一环路","phone":"18160153797","customerSeq":"KH250109000328","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.673609","longitude":"104.039684"}},{"id":"1961247441583980545","code":"WXGD250829000004","productId":"1730431526011556121","machineNum":"MC2501150162","deviceGroupId":"20000000194","customerId":"1000590000","customerStaffId":"1902262128190963714",
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  -51598a3b3cda","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/cf1a5a60-847b-11f0-ae1f-51598a3b3cda"}],"expectArriveTime":"2025-08-29 13:59:20","orderReceiveTime":"2025-08-29 16:27:58","prospectArriveTime":"2025-08-30 15:01:00","departureTime":"2025-08-30 14:38:28","actualArriveTime":"2025-08-30 20:31:02","sendReportTime":"2025-08-31 13:29:28","confirmReportTime":"2025-08-31 13:34:17","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"400.00","discountAmount":"1601.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"901.00","totalAmount":"1601.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815143298347010","code":"SCZJZY0026","name":"邱开超","isAvailable":true},"isEvaluated":false,"blackWhiteCount":229150,"colorCount":2064769,"fiveColourCount":0,"printCount":15456,"completedAt":"2025-08-31 13:34:17","createdAt":"2025-08-29 10:00:03","updatedAt":"2025-08-31 13:34:17","deleted":0,"serType":{"label":"购机半保","value":"BUY_HALF"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C5300S","customerName":"湘颖广告-江杨南路","subbranch":"江杨南路","phone":"18982412926","customerSeq":"KH250109000154","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"28.899758","longitude":"105.423598"}},{"id":"1961245745457774593","code":"WXGD250829000003","productId":"1730431526011556121","machineNum":"MC2501150154","deviceGroupId":"1881683584587264001","customerId":"1000170101","customerStaffId":"1894267310286315521","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"有墨","excPics":[{"key":"prod/e6be19a0-847a-11f0-89b4-f7b191fee879","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/e6be19a0-847a-11f0-89b4-f7b191fee879"}],"expectArriveTime":"2025-08-29 13:53:20","orderReceiveTime":"2025-08-29 15:18:05","prospectArriveTime":"2025-08-29 13:53:20","departureTime":"2025-08-29 15:18:17","actualArriveTime":"2025-08-30 17:35:56","sendReportTime":"2025-08-30 18:05:10","confirmReportTime":"2025-08-31 18:10:04","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"50.00","discountAmount":"510.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"160.00","totalAmount":"510.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","isAvailable":true},"isEvaluated":true,"blackWhiteCount":50362,"colorCount":968185,"fiveColourCount":0,"printCount":4913,"completedAt":"2025-08-31 18:10:05","createdAt":"2025-08-29 09:53:19","updatedAt":"2025-08-30 18:05:10","deleted":0,"serType":{"label":"购机半保","value":"BUY_HALF"},"deviceGroup":{"value":"704","label":"4号机"},"productInfo":"理光/Pro C5300S","customerName":"邑展图文-大邑","subbranch":"大邑","phone":"19302829238","customerSeq":"KH250109000089","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.569363","longitude":"103.517806"}},{"id":"1961245174558474241","code":"WXGD250829000002","productId":"1730431526011556129","machineNum":"MC2501150071","deviceGroupId":"1881587220985540609","customerId":"1004820000","customerStaffId":"1907316514193682433","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"打印效果不行","excPics":[{"key":"prod/9c2d81f0-847a-11f0-8ed9-dbf25fc6b139","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9c2d81f0-847a-11f0-8ed9-dbf25fc6b139"}],"expectArriveTime":"2025-08-29 13:50:20","orderReceiveTime":"2025-08-29 10:08:58","prospectArriveTime":"2025-08-29 13:50:20","departureTime":"2025-08-29 10:09:12","actualArriveTime":"2025-08-29 15:48:24","sendReportTime":"2025-08-29 15:56:30","confirmReportTime":"2025-08-30 16:00:07","repairP
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  "30.00","discountAmount":"750.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"420.00","totalAmount":"750.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","isAvailable":true},"isEvaluated":true,"blackWhiteCount":156242,"colorCount":526475,"fiveColourCount":0,"printCount":189,"completedAt":"2025-08-30 16:00:08","createdAt":"2025-08-29 09:51:02","updatedAt":"2025-08-29 15:56:30","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C5200S","customerName":"回形针-易龙物流园","subbranch":"易龙物流园","phone":"17761212885","customerSeq":"KH250109000337","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.527966","longitude":"104.177178"}},{"id":"1961230424260853762","code":"WXGD250829000001","productId":"1730431526003167399","machineNum":"MC2501150032","deviceGroupId":"1881666152724295682","customerId":"1881661470836649985","customerStaffId":"1901918674521010177","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"打印有问题","excPics":[{"key":"prod/6bff5880-8472-11f0-b51c-87d9dddffbf8","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/6bff5880-8472-11f0-b51c-87d9dddffbf8"}],"expectArriveTime":"2025-08-29 12:52:20","orderReceiveTime":"2025-08-29 10:18:47","prospectArriveTime":"2025-08-29 12:52:20","departureTime":"2025-08-29 10:18:55","actualArriveTime":"2025-08-29 11:50:14","sendReportTime":"2025-08-29 13:15:47","confirmReportTime":"2025-08-30 13:20:05","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","isAvailable":true},"isEvaluated":true,"blackWhiteCount":4737378,"colorCount":0,"fiveColourCount":0,"printCount":66750,"completedAt":"2025-08-30 13:20:05","createdAt":"2025-08-29 08:52:26","updatedAt":"2025-08-29 13:15:47","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"704","label":"4号机"},"productInfo":"理光/Pro 8120","customerName":"广州智印-金宇大厦","phone":"17761212885","customerSeq":"KH250121000004","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682451","longitude":"104.084481"}},{"id":"1961065580450660353","code":"WXGD250828000008","productId":"1877611669350264833","machineNum":"MC2504270001","deviceGroupId":"1922560714665369601","customerId":"1005990000","customerStaffId":"1888524512794898434","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"脏污","excPics":[{"key":"prod/e45b9700-8416-11f0-b644-3902fb617d72","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/e45b9700-8416-11f0-b644-3902fb617d72"}],"expectArriveTime":"2025-08-29 12:30:20","orderReceiveTime":"2025-08-29 09:03:40","prospectArriveTime":"2025-08-29 12:30:20","departureTime":"2025-08-29 09:03:48","actualArriveTime":"2025-08-29 09:45:55","sendReportTime":"2025-08-29 10:33:21","confirmReportTime":"2025-08-29 16:16:00","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"330.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"330.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":21883,"colorCount":862798,"fiveColourCount":0,"printCount":21511,"completedAt":"2025-08-29 16:16:00","cr
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  value":"PACKAGE_HALF"},"deviceGroup":{"value":"709","label":"9号机"},"productInfo":"理光/Pro C7500","customerName":"青于蓝-万兴路","subbranch":"万兴路","phone":"19940597089","customerSeq":"KH250109000296","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.817668","longitude":"104.227264"}},{"id":"1960989763464970241","code":"WXGD250828000007","productId":"1877611669350264833","deviceGroupId":"1930659319892680706","customerId":"1006240000","customerStaffId":"1930881335463780353","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","errorCode":"0","excDesc":"保养","excPics":[{"key":"prod/cf8202d0-83ec-11f0-a954-d921a35b5c89","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/cf8202d0-83ec-11f0-a954-d921a35b5c89"}],"expectArriveTime":"2025-08-29 11:26:20","orderReceiveTime":"2025-08-28 16:56:57","prospectArriveTime":"2025-08-29 11:26:20","departureTime":"2025-08-28 16:57:03","actualArriveTime":"2025-08-28 16:57:06","sendReportTime":"2025-08-28 17:34:27","confirmReportTime":"2025-08-29 17:35:04","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"855.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"525.00","totalAmount":"855.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":true,"blackWhiteCount":22116,"colorCount":743550,"fiveColourCount":0,"printCount":14204,"completedAt":"2025-08-29 17:35:05","createdAt":"2025-08-28 16:56:08","updatedAt":"2025-08-28 17:34:27","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C7500","customerName":"需求者印务-憩园街","subbranch":"憩园街","phone":"18682556258","customerSeq":"KH250109000022","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.816847","longitude":"104.0739"}},{"id":"1960972508077342721","code":"WXGD250828000006","productId":"1730431526003167387","machineNum":"MC2505230010","deviceGroupId":"1952672898590199809","customerId":"1001350000","customerStaffId":"1888468717835251713","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"保养","excPics":[{"key":"prod/3f0a2650-83e3-11f0-ac2c-c9949e082118","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/3f0a2650-83e3-11f0-ac2c-c9949e082118"}],"expectArriveTime":"2025-08-29 10:17:20","orderReceiveTime":"2025-08-28 15:52:03","prospectArriveTime":"2025-08-29 10:17:20","departureTime":"2025-08-28 15:52:10","actualArriveTime":"2025-08-28 15:52:16","sendReportTime":"2025-08-28 15:55:33","confirmReportTime":"2025-08-28 16:10:30","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"390.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"60.00","totalAmount":"390.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":false,"blackWhiteCount":155742,"colorCount":0,"fiveColourCount":0,"printCount":139644,"completedAt":"2025-08-28 16:10:30","createdAt":"2025-08-28 15:47:34","updatedAt":"2025-08-28 16:10:30","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"708","label":"8号机"},"productInfo":"理光/Pro 8200S","customerName":"蓉艺易印-荆竹园","subbranch":"荆竹园","phone":"18784522582","customerSeq":"KH250109000172","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.804696","longitude":"104.058368"}},{"id":"1960950448919789570","code":"WXGD250828000005","productId":"173043152600316
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  81","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"打印出现黑点","excPics":[{"key":"prod/fd009840-83d6-11f0-827f-a5705f8d2459","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/fd009840-83d6-11f0-827f-a5705f8d2459"}],"expectArriveTime":"2025-08-29 08:49:20","orderReceiveTime":"2025-08-29 09:16:47","prospectArriveTime":"2025-08-29 08:49:20","departureTime":"2025-08-29 09:17:16","actualArriveTime":"2025-08-29 10:52:44","sendReportTime":"2025-08-29 12:13:02","confirmReportTime":"2025-08-29 12:13:21","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"390.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"60.00","totalAmount":"390.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","isAvailable":true},"isEvaluated":false,"blackWhiteCount":1163378,"colorCount":0,"fiveColourCount":0,"printCount":27603,"completedAt":"2025-08-29 12:13:21","createdAt":"2025-08-28 14:19:54","updatedAt":"2025-08-29 12:13:21","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"703","label":"3号机"},"productInfo":"理光/Pro 8300S","customerName":"博之艺-学院路","subbranch":"学院路","phone":"17358626106","customerSeq":"KH250109000325","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.809661","longitude":"104.159117"}},{"id":"1960946770762645506","code":"WXGD250828000004","productId":"1730431526003167469","machineNum":"MC2501150147","deviceGroupId":"1881694874777022465","customerId":"1003410000","customerStaffId":"1943231342783262721","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"起杠子","excPics":[{"key":"prod/f15880e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f15880e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f197acc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f197acc0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1b546e0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1b546e0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1ce4d20-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1ce4d20-83d4-11f0-a497-2d258384740f"},{"key":"prod/f1fb77a0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f1fb77a0-83d4-11f0-a497-2d258384740f"},{"key":"prod/f25afcc0-83d4-11f0-a497-2d258384740f","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/f25afcc0-83d4-11f0-a497-2d258384740f"}],"expectArriveTime":"2025-08-29 08:35:20","orderReceiveTime":"2025-08-28 15:09:26","prospectArriveTime":"2025-08-29 08:35:20","departureTime":"2025-08-28 15:34:43","actualArriveTime":"2025-08-28 15:56:39","sendReportTime":"2025-08-28 17:44:20","confirmReportTime":"2025-08-28 17:46:36","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":1617191,"colorCount":1075963,"fiveColourCount":0,"printCount":6109,"completedAt":"2025-08-28 17:46:37","createdAt":"2025-08-28 14:05:17","updatedAt":"2025-08-28 17:46:37","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C7210X","customerName":"宾得包装-金泉路","subbranch":"金泉路","phone":"18582511764","customerSeq":"KH2
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  ":"GCJ_02"},"latitude":"30.715147","longitude":"104.013437"}},{"id":"1960939688621621249","code":"WXGD250828000003","productId":"1877611669350264833","machineNum":"MC2501150901","deviceGroupId":"1888415703229394945","customerId":"1001350000","customerStaffId":"1888468717835251713","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"卡纸","excPics":[{"key":"prod/07dbe360-83d1-11f0-858c-5f82e18bae8b","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/07dbe360-83d1-11f0-858c-5f82e18bae8b"}],"expectArriveTime":"2025-08-28 17:37:20","orderReceiveTime":"2025-08-28 13:51:00","prospectArriveTime":"2025-08-28 17:37:20","departureTime":"2025-08-28 13:51:07","actualArriveTime":"2025-08-28 14:32:41","sendReportTime":"2025-08-28 15:50:28","confirmReportTime":"2025-08-28 16:10:34","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"2068.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"1738.00","totalAmount":"2068.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":false,"blackWhiteCount":34020,"colorCount":3898174,"fiveColourCount":0,"printCount":32709,"completedAt":"2025-08-28 16:10:34","createdAt":"2025-08-28 13:37:09","updatedAt":"2025-08-28 16:10:34","deleted":0,"serType":{"label":"租赁半保","value":"RENT_HALF"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/Pro C7500","customerName":"蓉艺易印-荆竹园","subbranch":"荆竹园","phone":"18784522582","customerSeq":"KH250109000172","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.804696","longitude":"104.058368"}},{"id":"1960935225173327874","code":"WXGD250828000002","productId":"1730431526011556129","machineNum":"MC2501150024","deviceGroupId":"1938193047380619265","customerId":"1909907102785200129","customerStaffId":"1910130758928949249","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"WAIT_CONFIRM","excDesc":"更换鼓腊条","excPics":[{"key":"prod/8d774170-83ce-11f0-b36c-55f05093b7be","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/8d774170-83ce-11f0-b36c-55f05093b7be"}],"expectArriveTime":"2025-08-28 17:19:20","orderReceiveTime":"2025-08-29 08:52:40","prospectArriveTime":"2025-08-28 17:19:20","departureTime":"2025-08-29 08:53:00","actualArriveTime":"2025-08-29 15:18:23","sendReportTime":"2025-08-29 16:55:50","confirmReportTime":"2025-08-30 17:00:05","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"2286.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"1956.00","totalAmount":"2286.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","isAvailable":true},"isEvaluated":true,"blackWhiteCount":340379,"colorCount":1052,"fiveColourCount":0,"printCount":148587,"completedAt":"2025-08-30 17:00:05","createdAt":"2025-08-28 13:19:25","updatedAt":"2025-08-29 16:55:50","deleted":0,"serType":{"label":"购机全保","value":"BUY_FULL"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C5200S","customerName":"卓轩办公-北京路","subbranch":"北京路","phone":"17761212885","customerSeq":"KH250409000002","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.563533","longitude":"104.242079"}},{"id":"1960882486548414466","code":"WXGD250828000001","productId":"1730431526011556132","machineNum":"MC2503270037","deviceGroupId":"1938572719587119105","customerId":"1894607369652936706","customerStaffId":"1894943660454309890","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"双面套位差的?
2025-09-01 18:45:13.796  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  u.myqcloud.com/prod/460729d0-83b1-11f0-a76d-d5f125690055"}],"expectArriveTime":"2025-08-28 13:49:20","orderReceiveTime":"2025-08-28 11:15:50","prospectArriveTime":"2025-08-28 13:49:20","departureTime":"2025-08-28 11:18:03","actualArriveTime":"2025-08-28 12:09:10","sendReportTime":"2025-08-28 14:52:37","confirmReportTime":"2025-08-28 16:05:39","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"0.00","discountAmount":"300.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"300.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":2502615,"colorCount":539225,"fiveColourCount":0,"printCount":318194,"completedAt":"2025-08-28 16:05:40","createdAt":"2025-08-28 09:49:51","updatedAt":"2025-08-28 16:05:40","deleted":0,"serType":{"label":"租赁全保","value":"RENT_FULL"},"deviceGroup":{"value":"715","label":"15号机"},"productInfo":"理光/MP C8003","customerName":"通艺快印（青于蓝）-花牌坊","subbranch":"花牌坊","phone":"13666228335","customerSeq":"KH250226000003","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.682351","longitude":"104.049638"}},{"id":"1960683876170907649","code":"WXGD250827000016","productId":"1877611669350264833","deviceGroupId":"1930659319892680706","customerId":"1006240000","customerStaffId":"1931631663285018626","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","excDesc":"打出来的作业有杠","excPics":[{"key":"prod/01a05e60-8343-11f0-995d-4960103d9885","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/01a05e60-8343-11f0-995d-4960103d9885"},{"key":"prod/02901590-8343-11f0-995d-4960103d9885","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/02901590-8343-11f0-995d-4960103d9885"},{"key":"prod/03cc6620-8343-11f0-995d-4960103d9885","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/03cc6620-8343-11f0-995d-4960103d9885"}],"expectArriveTime":"2025-08-28 12:30:20","orderReceiveTime":"2025-08-27 20:54:56","prospectArriveTime":"2025-08-28 12:30:20","departureTime":"2025-08-27 20:55:03","actualArriveTime":"2025-08-27 22:02:09","sendReportTime":"2025-08-27 22:30:16","confirmReportTime":"2025-08-27 22:30:38","repairPay":"250.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"1290.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"960.00","totalAmount":"1290.00","totalPay":"0.00","isAssignEngineer":false,"engineerId":{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","isAvailable":true},"isEvaluated":false,"blackWhiteCount":729819,"colorCount":21643,"fiveColourCount":0,"printCount":33636,"completedAt":"2025-08-27 22:30:38","createdAt":"2025-08-27 20:40:38","updatedAt":"2025-08-27 22:30:38","deleted":0,"serType":{"label":"包量半保","value":"PACKAGE_HALF"},"deviceGroup":{"value":"702","label":"2号机"},"productInfo":"理光/Pro C7500","customerName":"需求者印务-憩园街","subbranch":"憩园街","phone":"15974003112","customerSeq":"KH250109000022","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.816847","longitude":"104.0739"}},{"id":"1960667333596856321","code":"WXGD250827000015","productId":"1730431526003167441","deviceGroupId":"1952177880834330625","customerId":"1952177623295676417","customerStaffId":"1952177623304065025","status":{"value":"completed","label":"已完成"},"isAppeal":false,"currentProcess":"DONE","errorCode":"0","excDesc":"sc324-00   sc324-01","excPics":[{"key":"prod/cc54f620-8339-11f0-baa3-0f2b77395afd","url":"https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/cc54f620-8339-11f0-baa3-0f2b77395afd"}],"expectArriveTime":"2025-08-28 12:30:20","orderRe
2025-09-01 18:45:13.797  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  19","actualArriveTime":"2025-08-31 11:19:31","sendReportTime":"2025-08-31 15:24:19","confirmReportTime":"2025-08-31 16:03:55","repairPay":"200.00","replacePay":"0.00","actualReplacePay":"0.00","visitPay":"50.00","longWayVisitPay":"30.00","discountAmount":"0.00","derateAmount":"0.00","additionalPay":"0.00","engineerAdditionalPay":"0.00","itemPay":"0.00","totalAmount":"280.00","totalPay":"280.00","isAssignEngineer":false,"engineerId":{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","isAvailable":true},"isEvaluated":false,"blackWhiteCount":1926814,"colorCount":0,"fiveColourCount":0,"printCount":7308,"completedAt":"2025-08-31 16:04:09","createdAt":"2025-08-27 19:34:54","updatedAt":"2025-08-31 16:04:02","deleted":0,"serType":{"label":"散修","value":"SCATTERED"},"payMode":{"label":"微信支付","value":"WECHART"},"deviceGroup":{"value":"701","label":"1号机"},"productInfo":"理光/MP 7502","customerName":"未好色彩图文广告-未登地址","subbranch":"未登地址","phone":"18030783073","customerSeq":"KH250804000001","laborAmount":"0.00","location":{"system":{"label":"火星/国测局坐标系","value":"GCJ_02"},"latitude":"30.812871","longitude":"104.079516"}}]}}
2025-09-01 18:45:13.797  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (89177-byte body)
2025-09-01 18:45:13.847  8985-8985  RepairOrders            com.example.repairorderapp           D  获取工单列表成功，返回50条数据
2025-09-01 18:45:13.847  8985-8985  RepairOrders            com.example.repairorderapp           D  总工单数: 1947, 当前页: 1
2025-09-01 18:45:13.847  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962688706003349505, 工单号=WXGD250902000004, 客户=枫创图文-灵龙东路, 机型=理光/Pro C5200S, 设备组=1号机, 服务类型=散修, 工程师=未分配, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.854  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962677127719432194, 工单号=WXGD250902000001, 客户=三五图文-一环路, 机型=理光/Pro 8220, 设备组=6号机, 服务类型=租赁全保, 工程师=邓顺中, productId=1730431526003167385, productInfo=理光/Pro 8220
2025-09-01 18:45:13.854  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962468768265285633, 工单号=WXGD250901000010, 客户=腾宇广告-西科大, 机型=理光/Pro C5300S, 设备组=2号机, 服务类型=购机半保, 工程师=曾惕, productId=1730431526011556121, productInfo=理光/Pro C5300S
2025-09-01 18:45:13.855  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962462956939755522, 工单号=WXGD250901000009, 客户=馨桃图书-茂书巷, 机型=理光/Pro 8320S, 设备组=1号机, 服务类型=购机全保, 工程师=未分配, productId=1730431526003167380, productInfo=理光/Pro 8320S
2025-09-01 18:45:13.856  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962460275647037442, 工单号=WXGD250901000008, 客户=东盛广告-华阳大道店, 机型=理光/MP 7503, 设备组=6号机, 服务类型=散修, 工程师=未分配, productId=1730431526003167421, productInfo=理光/MP 7503
2025-09-01 18:45:13.857  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962440991772360706, 工单号=WXGD250901000007, 客户=视崛图文-一环路, 机型=理光/Pro 8100S, 设备组=1号机, 服务类型=租赁全保, 工程师=邓顺中, productId=1730431526003167394, productInfo=理光/Pro 8100S
2025-09-01 18:45:13.857  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962436953295142913, 工单号=WXGD250901000006, 客户=嘉利图文-犀浦下街, 机型=理光/Pro 8110, 设备组=3号机, 服务类型=普通全保, 工程师=李逸晖, productId=1730431526003167397, productInfo=理光/Pro 8110
2025-09-01 18:45:13.858  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962436639523454977, 工单号=WXGD250901000005, 客户=广州智印-金宇大厦, 机型=理光/Pro 8120, 设备组=4号机, 服务类型=租赁全保, 工程师=邓顺中, productId=1730431526003167399, productInfo=理光/Pro 8120
2025-09-01 18:45:13.859  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962426750151540737, 工单号=WXGD250901000004, 客户=回形针-易龙物流园, 机型=理光/Pro C5200S, 设备组=3号机, 服务类型=租赁全保, 工程师=未分配, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.859  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962404386101641218, 工单号=WXGD250901000003, 客户=鸿达广告-滨河北路中段, 机型=理光/MP C5503, 设备组=1号机, 服务类型=散修, 工程师=未分配, productId=1730431526003167527, productInfo=理光/MP C5503
2025-09-01 18:45:13.859  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962401461937750017, 工单号=WXGD250901000002, 客户=邑展图文-大邑, 机型=理光/Pro C5300S, 设备组=4号机, 服务类型=购机半保, 工程师=苟自忠, productId=1730431526011556121, productInfo=理光/Pro C5300S
2025-09-01 18:45:13.859  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962308286405525505, 工单号=WXGD250901000001, 客户=大旗图文-太平南新街, 机型=理光/Pro C7500, 设备组=1号机, 服务类型=包量半保, 工程师=袁卿文, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.860  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962111792079552514, 工单号=WXGD250831000009, 客户=通艺快印（青于蓝）-花牌坊, 机型=理光/Pro C9100, 设备组=14号机, 服务类型=租赁半保, 工程师=舒茂林, productId=1730431526003167598, productInfo=理光/Pro C9100
2025-09-01 18:45:13.861  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962099320123670529, 工单号=WXGD250831000008, 客户=微熊文化-华翰路, 机型=理光/Pro C5200S, 设备组=3号机, 服务类型=租赁半保, 工程师=李逸晖, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.862  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962092265644081154, 工单号=WXGD250831000007, 客户=新凯江-普活路, 机型=理光/Pro C7500, 设备组=1号机, 服务类型=包量半保, 工程师=邓顺中, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.863  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962057112230150145, 工单号=WXGD250831000006, 客户=回形针-易龙物流园, 机型=理光/Pro C5200S, 设备组=2号机, 服务类型=租赁全保, 工程师=苟自忠, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.864  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962004924527132674, 工单号=WXGD250831000005, 客户=老实人-川音三巷, 机型=理光/MP 7503, 设备组=1号机, 服务类型=购机全保, 工程师=未分配, productId=1730431526003167421, productInfo=理光/MP 7503
2025-09-01 18:45:13.864  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1962001208910659585, 工单号=WXGD250831000004, 客户=蓉艺易印-荆竹园, 机型=理光/Pro 8200S, 设备组=6号机, 服务类型=租赁全保, 工程师=舒茂林, productId=1730431526003167387, productInfo=理光/Pro 8200S
2025-09-01 18:45:13.865  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961992445264773122, 工单号=WXGD250831000003, 客户=湘禾图文-大连北路, 机型=理光/MP C6503, 设备组=7号机, 服务类型=购机半保, 工程师=曾惕, productId=1730431526011556131, productInfo=理光/MP C6503
2025-09-01 18:45:13.866  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961968783048425474, 工单号=WXGD250831000002, 客户=金成图文-宁波路, 机型=爱普生/M21000A, 设备组=3号机, 服务类型=购机全保, 工程师=苟自忠, productId=1877609581216018434, productInfo=爱普生/M21000A
2025-09-01 18:45:13.868  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961886543304048642, 工单号=WXGD250831000001, 客户=需求者印务-憩园街, 机型=理光/Pro C7500, 设备组=2号机, 服务类型=包量半保, 工程师=舒茂林, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.869  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961742428276899841, 工单号=WXGD250830000003, 客户=京东图文-川师狮子山, 机型=理光/MP C6503, 设备组=5号机, 服务类型=租赁全保, 工程师=苟自忠, productId=1730431526011556131, productInfo=理光/MP C6503
2025-09-01 18:45:13.870  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961687492390404098, 工单号=WXGD250830000002, 客户=通艺快印（青于蓝）-花牌坊, 机型=理光/Pro C7500, 设备组=11号机, 服务类型=包量半保, 工程师=舒茂林, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.872  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961671658246881281, 工单号=WXGD250830000001, 客户=三五图文-一环路, 机型=理光/Pro 8220, 设备组=6号机, 服务类型=租赁全保, 工程师=李逸晖, productId=1730431526003167385, productInfo=理光/Pro 8220
2025-09-01 18:45:13.873  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961421144208818177, 工单号=WXGD250829000016, 客户=广印鸿图-营山, 机型=爱普生/M21000A, 设备组=7号机, 服务类型=购机全保, 工程师=未分配, productId=1877609581216018434, productInfo=爱普生/M21000A
2025-09-01 18:45:13.873  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961420583178715137, 工单号=WXGD250829000015, 客户=印拾光-蜀州北路, 机型=理光/MP C6503, 设备组=1号机, 服务类型=购机全保, 工程师=曾惕, productId=1730431526011556131, productInfo=理光/MP C6503
2025-09-01 18:45:13.876  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961420190260510721, 工单号=WXGD250829000014, 客户=印拾光-蜀州北路, 机型=理光/MP 7503, 设备组=2号机, 服务类型=普通半保, 工程师=曾惕, productId=1730431526003167421, productInfo=理光/MP 7503
2025-09-01 18:45:13.876  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961417993904832514, 工单号=WXGD250829000013, 客户=青于蓝-万兴路, 机型=理光/Pro C7500, 设备组=9号机, 服务类型=包量半保, 工程师=袁卿文, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.877  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961382994879037441, 工单号=WXGD250829000012, 客户=蓉艺易印-眉山, 机型=理光/Pro C9100, 设备组=1号机, 服务类型=租赁半保, 工程师=未分配, productId=1730431526003167598, productInfo=理光/Pro C9100
2025-09-01 18:45:13.878  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961342053535301633, 工单号=WXGD250829000011, 客户=通艺快印（青于蓝）-花牌坊, 机型=理光/Pro C9100, 设备组=14号机, 服务类型=租赁半保, 工程师=舒茂林, productId=1730431526003167598, productInfo=理光/Pro C9100
2025-09-01 18:45:13.879  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961335910016598017, 工单号=WXGD250829000010, 客户=杰斯顿-蓉台大道, 机型=理光/MP 6054, 设备组=1号机, 服务类型=租赁全保, 工程师=李逸晖, productId=1730431526011556067, productInfo=理光/MP 6054
2025-09-01 18:45:13.880  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961332975375335425, 工单号=WXGD250829000009, 客户=童画印务-童子街, 机型=理光/Pro 8100S, 设备组=3号机, 服务类型=散修, 工程师=苟自忠, productId=1730431526003167394, productInfo=理光/Pro 8100S
2025-09-01 18:45:13.881  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961327542854074370, 工单号=WXGD250829000008, 客户=天天图文-杉板桥, 机型=理光/MP C6503, 设备组=1号机, 服务类型=租赁全保, 工程师=李逸晖, productId=1730431526011556131, productInfo=理光/MP C6503
2025-09-01 18:45:13.881  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961306421664530434, 工单号=WXGD250829000007, 客户=微熊文化-华翰路, 机型=理光/Pro C5200S, 设备组=3号机, 服务类型=租赁半保, 工程师=邱开超, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.882  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961273120681738241, 工单号=WXGD250829000006, 客户=国药-科园南路（创鑫伟业）, 机型=理光/MP 6054, 设备组=2号机, 服务类型=租赁全保, 工程师=李逸晖, productId=1730431526011556067, productInfo=理光/MP 6054
2025-09-01 18:45:13.883  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961258101000749057, 工单号=WXGD250829000005, 客户=视崛图文-一环路, 机型=理光/Pro 8100S, 设备组=1号机, 服务类型=租赁全保, 工程师=李逸晖, productId=1730431526003167394, productInfo=理光/Pro 8100S
2025-09-01 18:45:13.884  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961247441583980545, 工单号=WXGD250829000004, 客户=湘颖广告-江杨南路, 机型=理光/Pro C5300S, 设备组=1号机, 服务类型=购机半保, 工程师=邱开超, productId=1730431526011556121, productInfo=理光/Pro C5300S
2025-09-01 18:45:13.885  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961245745457774593, 工单号=WXGD250829000003, 客户=邑展图文-大邑, 机型=理光/Pro C5300S, 设备组=4号机, 服务类型=购机半保, 工程师=曾惕, productId=1730431526011556121, productInfo=理光/Pro C5300S
2025-09-01 18:45:13.886  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961245174558474241, 工单号=WXGD250829000002, 客户=回形针-易龙物流园, 机型=理光/Pro C5200S, 设备组=2号机, 服务类型=租赁全保, 工程师=邓顺中, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.887  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961230424260853762, 工单号=WXGD250829000001, 客户=广州智印-金宇大厦, 机型=理光/Pro 8120, 设备组=4号机, 服务类型=租赁全保, 工程师=苟自忠, productId=1730431526003167399, productInfo=理光/Pro 8120
2025-09-01 18:45:13.887  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1961065580450660353, 工单号=WXGD250828000008, 客户=青于蓝-万兴路, 机型=理光/Pro C7500, 设备组=9号机, 服务类型=包量半保, 工程师=舒茂林, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.888  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960989763464970241, 工单号=WXGD250828000007, 客户=需求者印务-憩园街, 机型=理光/Pro C7500, 设备组=2号机, 服务类型=包量半保, 工程师=袁卿文, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.889  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960972508077342721, 工单号=WXGD250828000006, 客户=蓉艺易印-荆竹园, 机型=理光/Pro 8200S, 设备组=8号机, 服务类型=租赁全保, 工程师=袁卿文, productId=1730431526003167387, productInfo=理光/Pro 8200S
2025-09-01 18:45:13.890  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960950448919789570, 工单号=WXGD250828000005, 客户=博之艺-学院路, 机型=理光/Pro 8300S, 设备组=3号机, 服务类型=购机全保, 工程师=李逸晖, productId=1730431526003167377, productInfo=理光/Pro 8300S
2025-09-01 18:45:13.890  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960946770762645506, 工单号=WXGD250828000004, 客户=宾得包装-金泉路, 机型=理光/Pro C7210X, 设备组=1号机, 服务类型=购机全保, 工程师=舒茂林, productId=1730431526003167469, productInfo=理光/Pro C7210X
2025-09-01 18:45:13.891  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960939688621621249, 工单号=WXGD250828000003, 客户=蓉艺易印-荆竹园, 机型=理光/Pro C7500, 设备组=1号机, 服务类型=租赁半保, 工程师=袁卿文, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.892  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960935225173327874, 工单号=WXGD250828000002, 客户=卓轩办公-北京路, 机型=理光/Pro C5200S, 设备组=2号机, 服务类型=购机全保, 工程师=曾惕, productId=1730431526011556129, productInfo=理光/Pro C5200S
2025-09-01 18:45:13.893  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960882486548414466, 工单号=WXGD250828000001, 客户=通艺快印（青于蓝）-花牌坊, 机型=理光/MP C8003, 设备组=15号机, 服务类型=租赁全保, 工程师=舒茂林, productId=1730431526011556132, productInfo=理光/MP C8003
2025-09-01 18:45:13.894  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960683876170907649, 工单号=WXGD250827000016, 客户=需求者印务-憩园街, 机型=理光/Pro C7500, 设备组=2号机, 服务类型=包量半保, 工程师=袁卿文, productId=1877611669350264833, productInfo=理光/Pro C7500
2025-09-01 18:45:13.895  8985-8985  RepairOrderData         com.example.repairorderapp           D  处理工单数据：ID=1960667333596856321, 工单号=WXGD250827000015, 客户=未好色彩图文广告-未登地址, 机型=理光/MP 7502, 设备组=1号机, 服务类型=散修, 工程师=舒茂林, productId=1730431526003167441, productInfo=理光/MP 7502
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  工单状态统计:
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  待接单=4
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  工程师接单=2
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  工程师出发=3
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  关闭=4
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  待确认维修报告=7
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  已完成=28
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  工程师到达=1
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  待结算=1
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  工单状态值统计:
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  pending_orders=4
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  engineer_receive=2
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  engineer_departure=3
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  close=4
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  wait_confirmed_report=7
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  completed=28
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  engineer_arrive=1
2025-09-01 18:45:13.896  8985-8985  RepairOrderStats        com.example.repairorderapp           D  to_be_settled=1
2025-09-01 18:45:13.896  8985-8985  RepairOrders            com.example.repairorderapp           D  恢复列表位置: position=0, offset=0
2025-09-01 18:45:13.897  8985-8985  RepairOrders            com.example.repairorderapp           D  应用过滤后，显示50条工单
2025-09-01 18:45:13.897  8985-8985  RepairOrders            com.example.repairorderapp           D  恢复列表位置: position=0, offset=0
2025-09-01 18:45:13.909  8985-8985  RepairOrderAdapter      com.example.repairorderapp           D  绑定工单数据：ID=1962688706003349505, 位置=0, 客户=枫创图文-灵龙东路, 机型=理光/Pro C5200S, 工程师=未分配
2025-09-01 18:45:13.932  8985-8985  RepairOrderAdapter      com.example.repairorderapp           D  绑定工单数据：ID=1962677127719432194, 位置=1, 客户=三五图文-一环路, 机型=理光/Pro 8220, 工程师=邓顺中
2025-09-01 18:45:13.941  8985-8985  RepairOrderAdapter      com.example.repairorderapp           D  绑定工单数据：ID=1962468768265285633, 位置=2, 客户=腾宇广告-西科大, 机型=理光/Pro C5300S, 工程师=曾惕
2025-09-01 18:45:13.957  8985-8985  RepairOrderAdapter      com.example.repairorderapp           D  绑定工单数据：ID=1962462956939755522, 位置=3, 客户=馨桃图书-茂书巷, 机型=理光/Pro 8320S, 工程师=未分配
2025-09-01 18:45:23.610  8985-8985  RepairOrders            com.example.repairorderapp           D  保存列表位置: position=0, offset=11
2025-09-01 18:45:23.625  8985-8985  PermissionManager       com.example.repairorderapp           D  注册权限观察者，当前观察者数量: 2
2025-09-01 18:45:23.625  8985-8985  ProfileFragment         com.example.repairorderapp           D  权限已预加载，直接初始化功能按钮
2025-09-01 18:45:23.651  8985-8985  AutofillManager         com.example.repairorderapp           D  view not autofillable - not passing ime action check
2025-09-01 18:45:23.651  8985-8985  AutofillManager         com.example.repairorderapp           D  view not autofillable - not passing ime action check
2025-09-01 18:45:23.654  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderCount
2025-09-01 18:45:23.655  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:45:23.655  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 19c9a4e1-9af0-42b6-9024-20fffd99c636
2025-09-01 18:45:23.655  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-09-01 18:45:23.655  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:23.655  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:45:23.655  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:23.656  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:23.657  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:23.657  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:23.688  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=227.97ms min=1.28ms max=9586.86ms count=44
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (70ms)
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:42 GMT
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:23.726  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:23.727  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"pendingOrdersCount":"0","myWorkOrderCount":"0","appealOrderCount":"0"}}
2025-09-01 18:45:23.727  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (107-byte body)
2025-09-01 18:45:23.727  8985-8985  ProfileFragment         com.example.repairorderapp           D  获取工单数量成功：待接工单=0, 我的工单=0, 申诉单=0
2025-09-01 18:45:25.235  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=134.85ms min=8.66ms max=1322.91ms count=11
2025-09-01 18:45:25.271  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/dict-extend/member-page/1002?pageNumber=1&pageSize=1000
2025-09-01 18:45:25.271  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.271  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/dict-extend/member-page/1002?pageNumber=1&pageSize=1000
2025-09-01 18:45:25.272  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/dict-extend/member-page/1002?pageNumber=1&pageSize=1000
2025-09-01 18:45:25.272  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.272  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.273  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.273  8985-8985  PermissionManager       com.example.repairorderapp           D  移除权限观察者，当前观察者数量: 2
2025-09-01 18:45:25.273  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.273  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.273  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.274  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.274  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.274  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.274  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.274  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/dict-extend/member-page/1002?pageNumber=1&pageSize=1000 (78ms)
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.350  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.351  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"total":"29","rows":[{"id":"1942045278999732226","code":"SCZJZY0036","name":"曾惕","mobileNumber":"15214052295","identityCardNumber":"43252419980630403X","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1936963939241906178","code":"SCZJZY0035","name":"蔡清文","mobileNumber":"18702324604","identityCardNumber":"500223200310247050","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1932312690642919426","code":"SCZJZY0034","name":"杨辉","mobileNumber":"19136308032","identityCardNumber":"510823199709227771","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1913070166829187073","code":"SCZJZY0032","name":"冯硕","mobileNumber":"15282780316","identityCardNumber":"513721199908147533","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1905497620185456641","code":"SCZJZY0031","name":"至简智印","mobileNumber":"17340196452","identityCardNumber":"510108202405140118","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1904765971227267074","code":"BYYKP","name":"杨坤鹏","mobileNumber":"17778330163","identityCardNumber":"511012199704236312","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1903681764983873537","code":"SCZJZY0030","name":"袁卿文","mobileNumber":"15581152736","identityCardNumber":"******************","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1901815444382265345","code":"SCZJZY0027","name":"舒茂林","mobileNumber":"17321858976","identityCardNumber":"511028199906092916","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1901815143298347010","code":"SCZJZY0026","name":"邱开超","mobileNumber":"13696059457","identityCardNumber":"511028199806052917","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1901814909059051522","code":"SCZJZY0025","name":"罗吉","mobileNumber":"18981846322","identityCardNumber":"511304199610110017","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1901814664908615681","code":"SCZJZY0024","name":"廖宇超","mobileNumber":"19113271871","identityCardNumber":"513822200106268752","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1901814367423410177","code":"SCZJZY0023","name":"李逸晖","mobileNumber":"15928466083","identityCardNumber":"510602199709045457","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1899646859761176577","code":"SCZJZY0022","name":"黄磊","mobileNumber":"18740366653","identityCardNumber":"612326199507026514","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1893856355911999489","code":"SCZJZY0020","name":"吴万林","mobileNumber":"13359663504","identityCardNumber":"513701199504081018","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"
2025-09-01 18:45:25.352  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  :"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1874760806424391682","code":"SCZJZY00013","name":"邓彪","mobileNumber":"13730886952","identityCardNumber":"511012199904123628","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1874760624722948098","code":"SCZJZY00012","name":"刘崇文","mobileNumber":"18143358595","identityCardNumber":"511012199904123627","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1874760092138614786","code":"SCZJZY00009","name":"苟自忠","mobileNumber":"15284725426","identityCardNumber":"511012199904123624","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1874759946344607746","code":"SCZJZY00008","name":"徐浩","mobileNumber":"18982204968","identityCardNumber":"511012199904123623","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1874758610127433729","code":"SCZJZY00006","name":"罗修彬","mobileNumber":"15680249150","identityCardNumber":"511012199904123621","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"suspended","label":"休眠"},"isBuildIn":false},{"id":"1871488550566940674","code":"SCZJZY00005","name":"邓顺中","mobileNumber":"18103150751","identityCardNumber":"511011197711216468","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1871488194411810818","code":"SCZJZY00004","name":"刘诗杭","mobileNumber":"17761212885","identityCardNumber":"511011197711216463","sex":{"value":"female","label":"女"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1871487903473913857","code":"SCZJZY00003","name":"文大亮","mobileNumber":"15828659150","identityCardNumber":"511011197711216461","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1871487313813491714","code":"SCZJZY00001","name":"邹学良","mobileNumber":"18081069791","identityCardNumber":"511027198901210031","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1825759903092838402","code":"program","name":"小程序审核专用","mobileNumber":"13888888888","identityCardNumber":"511023198801010001","sex":{"value":"unknown","label":"未知"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":true},{"id":"1730210377884528641","code":"B0000004","name":"张明","mobileNumber":"18227302120","identityCardNumber":"511011199805052213","email":"<EMAIL>","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1730205532934926338","code":"********","name":"苏应来","mobileNumber":"15196843116","email":"<EMAIL>","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1730201458164396033","code":"B0000002","name":"唐恬","mobileNumber":"18981842300","identityCardNumber":"511023199802110123","email":"<EMAIL>","sex":{"value":"male","label":"男"},"type":{"value":"permanent","label":"内部用户"},"state":{"value":"normal","label":"正常"},"isBuildIn":false},{"id":"1730200832705589250","code":"B0000001","name":"王季春","mobileNumber":"13388190025","identityCard
2025-09-01 18:45:25.352  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (8531-byte body)
2025-09-01 18:45:25.357  8985-8985  EngineerManagement      com.example.repairorderapp           D  获取到响应: EngineerListResponse(code=200, msg='ok', data=0个工程师)
2025-09-01 18:45:25.358  8985-8985  EngineerManagement      com.example.repairorderapp           D  解析到 29 个工程师
2025-09-01 18:45:25.360  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1942045278999732226
2025-09-01 18:45:25.361  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.361  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1942045278999732226
2025-09-01 18:45:25.361  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1942045278999732226
2025-09-01 18:45:25.362  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.362  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1936963939241906178
2025-09-01 18:45:25.362  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.362  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.363  8985-9418  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1936963939241906178
2025-09-01 18:45:25.363  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.363  8985-9418  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1936963939241906178
2025-09-01 18:45:25.363  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.364  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.364  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1932312690642919426
2025-09-01 18:45:25.364  8985-9418  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.364  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.364  8985-9419  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1932312690642919426
2025-09-01 18:45:25.364  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.365  8985-9418  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.365  8985-9419  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1932312690642919426
2025-09-01 18:45:25.365  8985-9419  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.365  8985-9418  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.365  8985-9418  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.366  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.366  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1913070166829187073
2025-09-01 18:45:25.366  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.366  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1913070166829187073
2025-09-01 18:45:25.366  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.366  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1913070166829187073
2025-09-01 18:45:25.366  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.366  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.366  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.366  8985-9418  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.366  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.367  8985-9419  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.367  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.367  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.367  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.368  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.368  8985-9419  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.368  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.370  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.371  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.371  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.373  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.374  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1905497620185456641
2025-09-01 18:45:25.374  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.374  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.374  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.374  8985-9421  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1905497620185456641
2025-09-01 18:45:25.374  8985-9421  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1905497620185456641
2025-09-01 18:45:25.374  8985-9421  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.374  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.374  8985-9421  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.374  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.375  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.438  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1905497620185456641 (64ms)
2025-09-01 18:45:25.438  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.438  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.439  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.439  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.439  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.439  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1936963939241906178 (75ms)
2025-09-01 18:45:25.439  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.439  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.439  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.439  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.440  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.440  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.440  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.440  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.440  8985-9418  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.441  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1913070166829187073 (75ms)
2025-09-01 18:45:25.441  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.441  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.441  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.441  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.442  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.443  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1903681764983873537
2025-09-01 18:45:25.443  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.443  8985-9422  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1903681764983873537
2025-09-01 18:45:25.444  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.444  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1904765971227267074
2025-09-01 18:45:25.444  8985-9422  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1903681764983873537
2025-09-01 18:45:25.445  8985-9422  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.445  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.445  8985-9423  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1904765971227267074
2025-09-01 18:45:25.445  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.445  8985-9422  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.445  8985-9423  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1904765971227267074
2025-09-01 18:45:25.446  8985-9423  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.446  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.446  8985-9423  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.446  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.446  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.446  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.446  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.446  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.446  8985-9422  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.446  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.447  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.447  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.447  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.447  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.449  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1942045278999732226 (88ms)
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":2,"notReceiveNum":"0","receiveNum":"2","completedWorkNum":"0"}}
2025-09-01 18:45:25.450  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.452  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815444382265345
2025-09-01 18:45:25.452  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.452  8985-9421  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815444382265345
2025-09-01 18:45:25.453  8985-9421  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815444382265345
2025-09-01 18:45:25.453  8985-9421  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.453  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.456  8985-9421  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.456  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815143298347010
2025-09-01 18:45:25.456  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1932312690642919426 (88ms)
2025-09-01 18:45:25.456  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.456  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.456  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.456  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.456  8985-9421  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.457  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.457  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.457  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.457  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.457  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815143298347010
2025-09-01 18:45:25.458  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815143298347010
2025-09-01 18:45:25.458  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.458  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.459  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.459  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.459  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.461  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.461  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.461  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.461  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.461  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":4,"notReceiveNum":"0","receiveNum":"4","completedWorkNum":"0"}}
2025-09-01 18:45:25.461  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.462  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814909059051522
2025-09-01 18:45:25.463  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.463  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814909059051522
2025-09-01 18:45:25.463  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814909059051522
2025-09-01 18:45:25.463  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.463  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.463  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.463  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.464  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.465  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.492  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 7331KB AllocSpace bytes, 11(432KB) LOS objects, 49% free, 8948KB/17MB, paused 888us,6.551ms total 49.165ms
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1904765971227267074 (60ms)
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.506  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.506  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1903681764983873537 (62ms)
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.507  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.507  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":1,"notReceiveNum":"0","receiveNum":"1","completedWorkNum":"0"}}
2025-09-01 18:45:25.507  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.508  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814664908615681
2025-09-01 18:45:25.508  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.508  8985-9419  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814664908615681
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814664908615681
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.508  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814367423410177
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.508  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.509  8985-9419  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.509  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.509  8985-9423  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814367423410177
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814367423410177
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.509  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.509  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.509  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.509  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.509  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.518  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815143298347010 (62ms)
2025-09-01 18:45:25.518  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901815444382265345 (66ms)
2025-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":1,"notReceiveNum":"0","receiveNum":"1","completedWorkNum":"0"}}
2025-09-01 18:45:25.519  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.519  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814909059051522 (56ms)
2025-09-01 18:45:25.519  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.520  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.520  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.520  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.520  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.520  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1899646859761176577
2025-09-01 18:45:25.520  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.520  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.520  8985-9422  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1899646859761176577
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1899646859761176577
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.521  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.522  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.522  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.522  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.522  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1893856355911999489
2025-09-01 18:45:25.522  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.522  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.522  8985-9421  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1893856355911999489
2025-09-01 18:45:25.522  8985-9421  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1893856355911999489
2025-09-01 18:45:25.523  8985-9421  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.523  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.523  8985-9421  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.524  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.524  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.524  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.524  8985-9421  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.524  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1888059847765696514
2025-09-01 18:45:25.525  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.525  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.525  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1888059847765696514
2025-09-01 18:45:25.525  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.525  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.525  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.525  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1888059847765696514
2025-09-01 18:45:25.525  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.525  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.525  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.525  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.526  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.564  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814664908615681 (56ms)
2025-09-01 18:45:25.564  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.564  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.564  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.565  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.565  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.565  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.565  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.565  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1901814367423410177 (56ms)
2025-09-01 18:45:25.565  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":5,"notReceiveNum":"0","receiveNum":"5","completedWorkNum":"0"}}
2025-09-01 18:45:25.566  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.567  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760806424391682
2025-09-01 18:45:25.567  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.567  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760806424391682
2025-09-01 18:45:25.567  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760806424391682
2025-09-01 18:45:25.567  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.567  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.568  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.569  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.569  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.569  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.569  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760624722948098
2025-09-01 18:45:25.569  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.569  8985-9423  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760624722948098
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760624722948098
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.569  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.570  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1888059847765696514 (56ms)
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.581  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.582  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.582  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1899646859761176577 (65ms)
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.586  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.587  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.587  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.587  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760092138614786
2025-09-01 18:45:25.587  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.587  8985-9419  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760092138614786
2025-09-01 18:45:25.587  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1893856355911999489 (64ms)
2025-09-01 18:45:25.587  8985-9419  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760092138614786
2025-09-01 18:45:25.587  8985-9419  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.587  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.587  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.587  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874759946344607746
2025-09-01 18:45:25.587  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.587  8985-9419  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.587  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874759946344607746
2025-09-01 18:45:25.588  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874759946344607746
2025-09-01 18:45:25.588  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.588  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.588  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.588  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.588  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.588  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.589  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.589  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.589  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.589  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.589  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.589  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.589  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.589  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.590  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.590  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.590  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.590  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.590  8985-9419  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.590  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.590  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.590  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.590  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.590  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.590  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.591  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874758610127433729
2025-09-01 18:45:25.591  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.591  8985-9422  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874758610127433729
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874758610127433729
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.593  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760806424391682 (54ms)
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.622  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.622  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760624722948098 (53ms)
2025-09-01 18:45:25.622  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.623  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488550566940674
2025-09-01 18:45:25.623  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.623  8985-9421  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488550566940674
2025-09-01 18:45:25.623  8985-9421  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488550566940674
2025-09-01 18:45:25.623  8985-9421  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.623  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.623  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.623  8985-9421  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.624  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.624  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488194411810818
2025-09-01 18:45:25.624  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.624  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.625  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488194411810818
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488194411810818
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.625  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.625  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.645  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874759946344607746 (58ms)
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.646  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.647  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874760092138614786 (60ms)
2025-09-01 18:45:25.647  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.647  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.647  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1874758610127433729 (55ms)
2025-09-01 18:45:25.648  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.648  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.648  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487903473913857
2025-09-01 18:45:25.648  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.648  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.648  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.648  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.649  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.649  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.649  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.649  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.649  8985-9423  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487903473913857
2025-09-01 18:45:25.649  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.649  8985-9423  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487903473913857
2025-09-01 18:45:25.649  8985-9423  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.649  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.649  8985-9423  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.650  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":3,"notReceiveNum":"0","receiveNum":"3","completedWorkNum":"0"}}
2025-09-01 18:45:25.650  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.650  8985-9423  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.650  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.650  8985-9423  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.650  8985-9423  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.651  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.651  8985-9423  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.651  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.651  8985-9423  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.651  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487313813491714
2025-09-01 18:45:25.651  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.651  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487313813491714
2025-09-01 18:45:25.651  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487313813491714
2025-09-01 18:45:25.651  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.651  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.651  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.652  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.652  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.652  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.652  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.653  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.654  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.654  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.654  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.656  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1825759903092838402
2025-09-01 18:45:25.656  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.656  8985-9422  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1825759903092838402
2025-09-01 18:45:25.656  8985-9422  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1825759903092838402
2025-09-01 18:45:25.656  8985-9422  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.656  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.656  8985-9422  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.657  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.658  8985-9422  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488550566940674 (55ms)
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.678  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871488194411810818 (54ms)
2025-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.679  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":3,"notReceiveNum":"0","receiveNum":"3","completedWorkNum":"0"}}
2025-09-01 18:45:25.679  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.679  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.680  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.680  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.680  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730210377884528641
2025-09-01 18:45:25.680  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.680  8985-9419  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730210377884528641
2025-09-01 18:45:25.680  8985-9419  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730210377884528641
2025-09-01 18:45:25.680  8985-9419  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.680  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.680  8985-9419  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.680  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730205532934926338
2025-09-01 18:45:25.680  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.681  8985-9421  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730205532934926338
2025-09-01 18:45:25.681  8985-9421  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730205532934926338
2025-09-01 18:45:25.681  8985-9421  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.681  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.681  8985-9421  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.681  8985-9419  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.682  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.682  8985-9419  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.682  8985-9421  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.711  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487903473913857 (61ms)
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1871487313813491714 (59ms)
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.711  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.711  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.711  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.711  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.711  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.712  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.712  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1825759903092838402 (55ms)
2025-09-01 18:45:25.712  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.712  8985-9423  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.712  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.712  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.712  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730201458164396033
2025-09-01 18:45:25.712  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.712  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.712  8985-9406  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730201458164396033
2025-09-01 18:45:25.712  8985-9406  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730201458164396033
2025-09-01 18:45:25.712  8985-9406  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.713  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.713  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.713  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730200832705589250
2025-09-01 18:45:25.713  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.713  8985-9420  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730200832705589250
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.713  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.713  8985-9422  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.713  8985-9406  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.713  8985-9420  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730200832705589250
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:45:25.714  8985-9420  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730205532934926338 (55ms)
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.736  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.737  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.737  8985-9421  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.737  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730210377884528641 (56ms)
2025-09-01 18:45:25.737  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.737  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.738  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.738  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.738  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.738  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.739  8985-9419  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730201458164396033 (56ms)
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.769  8985-9406  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer-info/getEngineerWorkSummary/1730200832705589250 (56ms)
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:40:44 GMT
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:45:25.770  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=*************-09-01 18:45:25.771  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"todayWorkNum":0,"notReceiveNum":"0","receiveNum":"0","completedWorkNum":"0"}}
2025-09-01 18:45:25.771  8985-9420  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (113-byte body)
2025-09-01 18:45:25.775  8985-8985  EngineerManagement      com.example.repairorderapp           D  按剩余工单数排序后的工程师列表:
2025-09-01 18:45:25.775  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 0: 李逸晖, 剩余工单数: 5
2025-09-01 18:45:25.775  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 1: 杨辉, 剩余工单数: 4
2025-09-01 18:45:25.775  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 2: 苟自忠, 剩余工单数: 3
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 3: 邓顺中, 剩余工单数: 3
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 4: 曾惕, 剩余工单数: 2
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 5: 袁卿文, 剩余工单数: 1
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 6: 舒茂林, 剩余工单数: 1
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 7: 蔡清文, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 8: 冯硕, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 9: 至简智印, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 10: 杨坤鹏, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 11: 邱开超, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 12: 罗吉, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 13: 廖宇超, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 14: 黄磊, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 15: 吴万林, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 16: 何强, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 17: 邓彪, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 18: 刘崇文, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 19: 徐浩, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 20: 罗修彬, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 21: 刘诗杭, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 22: 文大亮, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 23: 邹学良, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 24: 小程序审核专用, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 25: 张明, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 26: 苏应来, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 27: 唐恬, 剩余工单数: 0
2025-09-01 18:45:25.776  8985-8985  EngineerManagement      com.example.repairorderapp           D  排序位置 28: 王季春, 剩余工单数: 0
2025-09-01 18:45:28.757  8985-9016  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
