--------- beginning of main
--------- beginning of system
2025-09-01 14:38:30.313  4206-4288  <PERSON><PERSON>                  pid-4206                             E  [62] ItemStore: getItems RPC failed for item com.example.repairorderapp
2025-09-01 14:40:44.288  4671-4842  beacon                  pid-4671                             E  BeaconSDK init success! appkey is: 0AND05KOZX0E3L2H, packageName is: com.example.repairorderapp
--------- beginning of crash
2025-09-01 18:27:31.328   606-1918  AppOps                  system_server                        E  Operation not started: uid=10228 pkg=com.example.repairorderapp(null) op=WAKE_LOCK
---------------------------- PROCESS STARTED (8985) for package com.example.repairorderapp ----------------------------
2025-09-01 18:33:26.093  8985-8985  ApplicationLoaders      com.example.repairorderapp           D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.extensions.jar
2025-09-01 18:33:26.093  8985-8985  ApplicationLoaders      com.example.repairorderapp           D  Returning zygote-cached class loader: /system_ext/framework/androidx.window.sidecar.jar
2025-09-01 18:33:26.103  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes14.dm': No such file or directory
2025-09-01 18:33:26.106  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes10.dm': No such file or directory
2025-09-01 18:33:26.113  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
2025-09-01 18:33:26.116  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes12.dm': No such file or directory
2025-09-01 18:33:26.119  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes13.dm': No such file or directory
2025-09-01 18:33:26.123  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes15.dm': No such file or directory
2025-09-01 18:33:26.131  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes16.dm': No such file or directory
2025-09-01 18:33:26.136  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-09-01 18:33:26.147  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes4.dm': No such file or directory
2025-09-01 18:33:26.152  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes6.dm': No such file or directory
2025-09-01 18:33:26.164  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes7.dm': No such file or directory
2025-09-01 18:33:26.167  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
2025-09-01 18:33:26.169  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/data/com.example.repairorderapp/code_cache/.overlay/base.apk/classes9.dm': No such file or directory
2025-09-01 18:33:26.172  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.dm': No such file or directory
2025-09-01 18:33:26.172  8985-8985  ziparchive              com.example.repairorderapp           W  Unable to open '/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.dm': No such file or directory
2025-09-01 18:33:26.650  8985-8985  nativeloader            com.example.repairorderapp           D  Configuring clns-7 for other apk /data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/lib/x86_64:/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.example.repairorderapp
2025-09-01 18:33:26.679  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Currently set values for:
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V    angle_gl_driver_selection_pkgs=[]
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V    angle_gl_driver_selection_values=[]
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-09-01 18:33:26.680  8985-8985  GraphicsEnvironment     com.example.repairorderapp           V  Neither updatable production driver nor prerelease driver is supported.
2025-09-01 18:33:26.685  8985-8985  ActivityThread          com.example.repairorderapp           W  Application com.example.repairorderapp is suspending. Debugger needs to resume to continue.
2025-09-01 18:33:26.686  8985-8985  System.out              com.example.repairorderapp           I  Sending WAIT chunk
2025-09-01 18:33:26.686  8985-8985  System.out              com.example.repairorderapp           I  Waiting for debugger first packet
2025-09-01 18:33:26.805  8985-8988  nativeloader            com.example.repairorderapp           D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-09-01 18:33:26.988  8985-8985  System.out              com.example.repairorderapp           I  Debug.suspendAllAndSentVmStart
2025-09-01 18:33:27.396  8985-8985  System.out              com.example.repairorderapp           I  Debug.suspendAllAndSendVmStart, resumed
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM with version 2.1.0 has multidex support
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  Installing application
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM has multidex support, MultiDex support library is disabled.
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  Installing application
2025-09-01 18:33:27.536  8985-8985  MultiDex                com.example.repairorderapp           I  VM has multidex support, MultiDex support library is disabled.
2025-09-01 18:33:27.612  8985-8985  WM-WrkMgrInitializer    com.example.repairorderapp           D  Initializing WorkManager with default configuration.
2025-09-01 18:33:27.731  8985-8985  WM-PackageManagerHelper com.example.repairorderapp           D  Skipping component enablement for androidx.work.impl.background.systemjob.SystemJobService
2025-09-01 18:33:27.731  8985-8985  WM-Schedulers           com.example.repairorderapp           D  Created SystemJobScheduler and enabled SystemJobService
2025-09-01 18:33:27.842  8985-9005  CompatChangeReporter    com.example.repairorderapp           D  Compat change id reported: 253665015; UID 10228; state: ENABLED
2025-09-01 18:33:28.040  8985-8985  RemoteConfigManager     com.example.repairorderapp           D  API客户端初始化成功（使用Token认证）
2025-09-01 18:33:28.069  8985-8985  RemoteConfigManager     com.example.repairorderapp           I  RemoteConfigManager初始化完成，等待登录成功后更新配置
2025-09-01 18:33:28.083  8985-8985  EnhancedLogCollector    com.example.repairorderapp           I  日志收集器已初始化
2025-09-01 18:33:28.084  8985-8985  EnhancedLogUtils        com.example.repairorderapp           D  增强日志系统已初始化
2025-09-01 18:33:28.096  8985-9009  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:28.096  8985-9009  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:28.111  8985-8985  RepairOrderApp          com.example.repairorderapp           I  增强日志系统初始化成功
2025-09-01 18:33:28.114  8985-9011  RemoteConfigManager     com.example.repairorderapp           I  加载缓存配置成功: 1.0.0
2025-09-01 18:33:28.114  8985-9011  RemoteConfigManager     com.example.repairorderapp           I  🔔 通知配置更新: 上传间隔=300秒
2025-09-01 18:33:28.114  8985-9011  EnhancedLogCollector    com.example.repairorderapp           D  配置已更新: 1.0.0
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           D  设置全局协程异常处理器
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           I  全局异常处理器设置完成
2025-09-01 18:33:28.123  8985-8985  GlobalExceptionHandler  com.example.repairorderapp           I  全局异常处理器已初始化
2025-09-01 18:33:28.124  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局异常处理器初始化成功
2025-09-01 18:33:28.126  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局异常监控服务启动成功
2025-09-01 18:33:28.132  8985-8985  CrashHandler            com.example.repairorderapp           I  全局异常处理器已安装
2025-09-01 18:33:28.132  8985-8985  RepairOrderApp          com.example.repairorderapp           I  全局崩溃处理器安装成功
2025-09-01 18:33:28.134  8985-8985  AppLifecycleTracker     com.example.repairorderapp           I  应用生命周期跟踪器已注册
2025-09-01 18:33:28.134  8985-8985  RepairOrderApp          com.example.repairorderapp           I  应用生命周期跟踪器注册成功
2025-09-01 18:33:28.153  8985-8985  LogUploadManager        com.example.repairorderapp           I  🔧 LogUploadManager开始初始化...
2025-09-01 18:33:28.158  8985-8985  LogUploadManager        com.example.repairorderapp           D  API客户端初始化成功（使用Token认证）
2025-09-01 18:33:28.172  8985-8985  LogUploadManager        com.example.repairorderapp           D  日志上传仓库初始化成功
2025-09-01 18:33:28.173  8985-8985  LogUploadManager        com.example.repairorderapp           I  测试模式：使用纯远程配置，上传间隔: 300秒
2025-09-01 18:33:28.173  8985-8985  LogUploadManager        com.example.repairorderapp           I  LogUploadManager 初始化完成，使用简化防重复机制
2025-09-01 18:33:28.174  8985-8985  LogUploadManager        com.example.repairorderapp           I  日志上传管理器已初始化（智能调度模式）
2025-09-01 18:33:28.174  8985-8985  LogUploadManager        com.example.repairorderapp           D  === 运行时配置摘要 ===
构建类型: debug
调试模式: true
版本信息: 1.0.3-debug (1)
🔧 实际运行配置:
日志上传间隔: 300秒
位置日志间隔: 600秒
WorkManager间隔: 1分钟
日志级别: INFO
最大日志文件: 10MB
最大文件数量: 5个
详细日志: true
网络超时: 10秒
配置来源: 默认配置
2025-09-01 18:33:28.175  8985-9012  LogUploadManager        com.example.repairorderapp           I  开始清理历史问题数据...
2025-09-01 18:33:28.175  8985-8985  LogUploadManager        com.example.repairorderapp           I  启动定时上传任务，间隔: 300秒
2025-09-01 18:33:28.175  8985-8985  LogUploadManager        com.example.repairorderapp           I  🚀 定时上传任务已启动，上传间隔: 300秒
2025-09-01 18:33:28.176  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  设备数据上传管理器已初始化
2025-09-01 18:33:28.176  8985-8985  RepairOrderApp          com.example.repairorderapp           I  设备数据上传管理器初始化成功
2025-09-01 18:33:28.176  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:28.178  8985-8985  TokenFixer              com.example.repairorderapp           D  开始检查令牌状态...
2025-09-01 18:33:28.179  8985-8985  TokenFixer              com.example.repairorderapp           D  令牌有效
2025-09-01 18:33:28.179  8985-9011  LogUploadManager        com.example.repairorderapp           I  🎯 执行首次立即上传...
2025-09-01 18:33:28.179  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-09-01 18:33:28.179  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:28.180  8985-8985  RepairOrderApp          com.example.repairorderapp           I  TencentMap SDK 隐私协议已同意
2025-09-01 18:33:28.180  8985-9011  LogUploadManager        com.example.repairorderapp           I  📤 开始定时上传日志...
2025-09-01 18:33:28.182  8985-9016  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:28.183  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  允许执行操作: upload_logs_1730205532934926338
2025-09-01 18:33:28.185  8985-9011  SimpleRequestGuard      com.example.repairorderapp           D  开始执行操作: upload_logs_1730205532934926338
2025-09-01 18:33:28.196  8985-8985  BootCompletedReceiver   com.example.repairorderapp           D  BOOT_COMPLETED received, scheduling location worker.
2025-09-01 18:33:28.206  8985-9012  LogUploadManager        com.example.repairorderapp           D  没有发现需要清理的历史数据
2025-09-01 18:33:28.211  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 批量上传日志 - 类型数: 2
2025-09-01 18:33:28.211  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传PERFORMANCE日志 - 数量: 6
2025-09-01 18:33:28.213  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:33:28.213  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:33:28.237  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:28.240  8985-8985  Choreographer           com.example.repairorderapp           I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-09-01 18:33:28.257  8985-9009  DeviceInfoCollector     com.example.repairorderapp           I  设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:28.294  8985-9016  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:28.300  8985-9016  DeviceDataUploadManager com.example.repairorderapp           D  设备信息无变化，跳过上传
2025-09-01 18:33:28.305  8985-9016  DeviceDataUploadManager com.example.repairorderapp           D  没有待上传的崩溃信息
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:33:28.330  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 3497
2025-09-01 18:33:28.332  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722109853,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2721,\"memoryUsage\":14321344}","id":1521,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2721ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:21:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722169856,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2713,\"memoryUsage\":15249760}","id":1523,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2713ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:22:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722229918,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2757,\"memoryUsage\":15678256}","id":1525,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2757ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:23:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722289889,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2716,\"memoryUsage\":16196864}","id":1527,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2716ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:24:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722349906,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2719,\"memoryUsage\":8546960}","id":1529,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2719ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:25:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722409927,"deviceId":"cf7f6ce27817ef1a","extraData":"{\"duration\":2726,\"memoryUsage\":9225312}","id":1531,"isUploaded":false,"level":"INFO","logType":"PERFORMANCE","message":"性能监控: location_acquisition - 耗时: 2726ms","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"PerformanceMonitor","timestamp":"2025-09-01 10:26:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:33:28.332  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (3497-byte body)
2025-09-01 18:33:28.332  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.332  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.332  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:28.333  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:28.333  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.343  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:28.344  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.376  8985-8985  AppCompatDelegate       com.example.repairorderapp           D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-09-01 18:33:28.407  8985-9047  TrafficStats            com.example.repairorderapp           D  tagSocket(112) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:28.430  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: SplashActivity
2025-09-01 18:33:28.504  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (unsupported, reflection, allowed)
2025-09-01 18:33:28.505  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
2025-09-01 18:33:28.509  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 SplashActivity 设置全局触摸监听
2025-09-01 18:33:28.693  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.693  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.694  8985-9047  TokenInterceptor        com.example.repairorderapp           I  Token过期次数: 1/3
2025-09-01 18:33:28.695  8985-8985  RepairOrderApp          com.example.repairorderapp           D  设置启动页面状态: true
2025-09-01 18:33:28.695  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (362ms)
2025-09-01 18:33:28.695  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:47 GMT
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:28.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:28.697  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: b655fb92dd974d6fbaccd93690d7797f
2025-09-01 18:33:28.697  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:28.699  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:28.699  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:28.710  8985-8985  SplashActivity          com.example.repairorderapp           D  知识库缓存和状态已在应用启动时清除
2025-09-01 18:33:28.712  8985-9012  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传PERFORMANCE日志 成功 (6条) - 响应: 会话过期
2025-09-01 18:33:28.712  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络操作: 上传BUSINESS日志 - 数量: 5
2025-09-01 18:33:28.713  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  网络状态检查: hasInternet=true, isValidated=true
2025-09-01 18:33:28.713  8985-9012  BaseNetworkRepository   com.example.repairorderapp           D  执行网络请求，尝试次数: 1/4
2025-09-01 18:33:28.715  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: SplashActivity
2025-09-01 18:33:28.719  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: SplashActivity
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json
2025-09-01 18:33:28.726  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 2767
2025-09-01 18:33:28.727  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","deviceId":"cf7f6ce27817ef1a","logs":[{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722169856,"deviceId":"cf7f6ce27817ef1a","id":1522,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:22:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722229919,"deviceId":"cf7f6ce27817ef1a","id":1524,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:23:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722289889,"deviceId":"cf7f6ce27817ef1a","id":1526,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:24:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722349906,"deviceId":"cf7f6ce27817ef1a","id":1528,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:25:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"},{"appVersion":"1.0.3-debug","brand":"google","createTime":1756722409929,"deviceId":"cf7f6ce27817ef1a","id":1530,"isUploaded":false,"level":"ERROR","logType":"BUSINESS","message":"位置获取失败: 错误码\u003d404, 原因\u003dERROR_SERVER_NOT_LOCATION","model":"sdk_gphone64_x86_64","osType":"Android","osVersion":"Android 15","sdkVersion":35,"sessionId":"9ae1dddb-c6e9-437d-8e6e-fd8e69895824","tag":"LocationUpdateService","timestamp":"2025-09-01 10:26:49","userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}]}
2025-09-01 18:33:28.727  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (2767-byte body)
2025-09-01 18:33:28.728  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:28.729  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:28.730  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:28.731  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:28.732  8985-8985  HWUI                    com.example.repairorderapp           W  Unknown dataspace 0
2025-09-01 18:33:28.744  8985-8985  GlobalExceptionMonitor  com.example.repairorderapp           I  全局异常监控服务已启动
2025-09-01 18:33:28.746  8985-8985  TokenInterceptor        com.example.repairorderapp           D  获取当前Activity: SplashActivity
2025-09-01 18:33:28.746  8985-8985  TokenInterceptor        com.example.repairorderapp           I  准备显示Token过期对话框 (第1次)，当前Activity: SplashActivity
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/log/upload
2025-09-01 18:33:28.782  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/logcontrol/log/upload (剩余冷却时间: 9秒)
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/log/upload (54ms)
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:47 GMT
2025-09-01 18:33:28.782  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: fb6c9e95bb0d4d77af2081cc772929e7
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:28.783  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:28.785  8985-9016  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 上传BUSINESS日志 成功 (5条) - 响应: 会话过期
2025-09-01 18:33:28.785  8985-9016  BaseNetworkRepository   com.example.repairorderapp           I  ✅ 批量上传日志 成功 (2条) - 全部成功
2025-09-01 18:33:28.789  8985-9016  LogUploadManager        com.example.repairorderapp           D  标记6条日志为已上传
2025-09-01 18:33:28.789  8985-9016  LogUploadManager        com.example.repairorderapp           D  ✅ PERFORMANCE日志上传成功: 6条
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  标记5条日志为已上传
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  ✅ BUSINESS日志上传成功: 5条
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 日志上传成功，总计: 11条
2025-09-01 18:33:28.790  8985-9016  SimpleRequestGuard      com.example.repairorderapp           D  操作执行完成: upload_logs_1730205532934926338
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           I  ✅ 定时上传成功
2025-09-01 18:33:28.790  8985-9016  LogUploadManager        com.example.repairorderapp           D  ⏰ 等待下次上传，间隔: 300秒 (300000ms)
2025-09-01 18:33:28.801  8985-8989  .repairorderapp         com.example.repairorderapp           I  Compiler allocated 4219KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
2025-09-01 18:33:28.820  8985-8985  TokenInterceptor        com.example.repairorderapp           I  Token过期对话框显示成功 (第1次)
2025-09-01 18:33:28.923  8985-9041  EGL_emulation           com.example.repairorderapp           I  Opening libGLESv1_CM_emulation.so
2025-09-01 18:33:28.923  8985-9041  EGL_emulation           com.example.repairorderapp           I  Opening libGLESv2_emulation.so
2025-09-01 18:33:28.957  8985-9041  HWUI                    com.example.repairorderapp           W  Failed to choose config with EGL_SWAP_BEHAVIOR_PRESERVED, retrying without...
2025-09-01 18:33:28.957  8985-9041  HWUI                    com.example.repairorderapp           W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-09-01 18:33:29.014  8985-9041  Gralloc4                com.example.repairorderapp           I  mapper 4.x is not supported
2025-09-01 18:33:29.161  8985-8989  .repairorderapp         com.example.repairorderapp           I  Compiler allocated 5174KB to compile void android.view.ViewRootImpl.performTraversals()
2025-09-01 18:33:30.155  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=13.21ms min=1.98ms max=36.50ms count=60
2025-09-01 18:33:30.437  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=120.64ms min=12.86ms max=1029.70ms count=10
2025-09-01 18:33:30.719  8985-8985  TokenFixer              com.example.repairorderapp           D  开始检查令牌状态...
2025-09-01 18:33:30.719  8985-8985  TokenFixer              com.example.repairorderapp           D  令牌有效
2025-09-01 18:33:30.723  8985-8985  TokenFixer              com.example.repairorderapp           D  验证令牌有效性: 25b4ac48-5...
2025-09-01 18:33:30.730  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_verifyToken
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 25b4ac48-577e-4008-8fe1-5578b1ccdf0c
2025-09-01 18:33:30.734  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:30.734  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.734  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 25b4ac48-5...
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:30.735  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:30.792  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token过期处理在冷却期内，跳过: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (剩余冷却时间: 7秒)
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (57ms)
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:49 GMT
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 3c2bdb93243047168aabc3002f6824f0
2025-09-01 18:33:30.792  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:30.793  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:30.793  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:30.795  8985-8985  TokenManager            com.example.repairorderapp           W  验证令牌返回非成功状态: 401, 消息:
2025-09-01 18:33:30.795  8985-8985  SplashActivity          com.example.repairorderapp           W  令牌远程验证失败，Token可能已过期，需要重新登录
2025-09-01 18:33:30.795  8985-8985  SplashActivity          com.example.repairorderapp           I  已清除过期Token，跳转到登录页面
2025-09-01 18:33:30.795  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: app_startup - 耗时: 2281ms [耗时: 2281ms] [内存: 11506KB]
2025-09-01 18:33:30.814  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity暂停: SplashActivity
2025-09-01 18:33:30.846  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: LoginActivity
2025-09-01 18:33:30.849  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 LoginActivity 设置全局触摸监听
2025-09-01 18:33:30.953  8985-9066  ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn
2025-09-01 18:33:30.968  8985-9066  TrafficStats            com.example.repairorderapp           D  tagSocket(146) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:30.972  8985-8985  LoginActivity           com.example.repairorderapp           D  开始获取验证码...
2025-09-01 18:33:30.974  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getCaptcha
2025-09-01 18:33:30.976  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-09-01 18:33:30.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-09-01 18:33:30.977  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:30.977  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:30.978  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:30.979  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:30.979  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: LoginActivity
2025-09-01 18:33:30.980  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: LoginActivity
2025-09-01 18:33:31.074  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/captcha (97ms)
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:28:49 GMT
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:31.075  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:31.076  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"1ae86997-f10d-44d9-8157-cd24a535f214","second":"data:image/gif;base64,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
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+////CP4A/wkcSLCgwYMIEypcyLChw4cQI0qcSLGixYsYM2rcyDFYqlGZPqUCZoyjyZMoB7oa9WvYv2HAUqUSlrKmTYrBRrksCGwUsZtAgypM9Quhq1RCkyr9pxPhsFAVfy5tKJVhsYyZFGadSMwUsKkJxfmiJeuqwl+xRpm12PTgsE8VfaGqClYgMFyyZvXaeZBYr1Wn9mYciZAV0oquZNX955fsra8Jg7kKJavoxphuQ9GsWIxVr6ljZdHytdag2FeiYG3maIwwwWGmXGEUxlJosF15ea0+KIwXKVakZzsNxcrlMFahZGf8NWr3ycaybFlO+EsWqFeQMXKawan0QGIfQ/7O5NiLlXeNv8jS6nWeoF9VqnrRxdhmxows2ZPKUk6xJMHes8yi20LAvAJKZSmlYt8Mk8x3EzGo+NKfEFwcososZNUyHULF+MJKKQPaJMwcC37SXkrAdIJKJqe44qBCxvxSiRpLCHEGJa2cKJkor/giTlK+QGHfFKsERQwrqpQiDDCyqPKiQQDKsksw/xQzjEyQTCJTKr740sss+1E5VTGqLNiFhDbxolhiAu2nUIfqbShQl13KBIoikSQCnH91FVPKglOUwpdJqnzV2WfBnIIQMLaIJh9CdHpJyyu0mCKTJkKI0QgrTwLl54LciclRJjvR9ksxWw00DC9gTrlQl/68ACYYQcYMA0odSQzhBqkn1lTMKlksmEZwGhU6EHO/KPqPWOqhuVAwteQlJ4etUJKGEE/sYcowfOIEDDDzBfMtMGL+Aoh9VHyxyKkD8eKuXb24y8tCav7jZS+tJAfMLXkJBkwvvThL0Gmi2NKlROIQowokg+QhcEHp0bJLQksCM2gx43671pVuULGgIafKW0y88s6rEDGryOIlL7RwkjIu+d2b3z+9/UYanXHRQovJB7E8WkIwkftaxhYTJG4rk4A6Ayai+VLyuwsRw0sqGOpMymevAczesdZhNxDOFBGjMy2Dqjp22QRh/O1/34oLzG7jXgXMJEIueIgorcys0P5tou3sCzCh5PcLwBK+F998dALzS5e/vP1jQz4/PKfOPCPkdlVuBy0qMW0XNIwsnyg9gx2fgNtXL2TZAq7WxMhVlda/GIjgQZHW7ktLDUWcy0G56DxtQRXvxLnQbpsVtHP/yNsLJ3mIbkUfXAYT2mhr3VsUm/8E00vvICJPkO21476QOLXoLKpAwYwNjC+r6C2Q5u9/S1Pm6H+LtryiBrOKIWCIbh8eWmrcT7QHsCp5Jhi40FkvHneWYHBLILVaH+N6NRDU7WxxpZgEIBwBikyAahIcGpdAgvcS+dlFYwaRFwPdw4tQzKAM/lsQGF4xi1q4wkOMwMTOHlWRYdDJgf7i+sUvHrEFUFEBDZmghCKUtghKUMI+ZuhDkSz3rQFWsUqdG9752gU1gvwiWusx3vo+YYg0gKoNecjDuewjhzQiYgZWMIMc09AHQNjxjni0ox3kaIYpvOEQhIhDDJWWiEpwQg52SAQnWiEL0TgkeGrLXxWP17MuAkhA3itIMYKxOLzIAhFtAAMg0jjIUvqPEIQIhH2qMAMo4AEQkMgEl36hiT1gCGv/sCAuF6I5So7QfvRLobtGFqeJDK4XRdGezoYRRCEuDj7QhM8qnElNcknvYMUAw2EOsgpQ6OwqxRhbJg2iNnLZT1XyK17PyueoTjFkGFrrULx6hJEO3c5eaf6gYCcoVZSITewhbovbQMrZOeCpSTRbpIj1YGcKdzbkml+Bg+QK4gpS7Cx5OpsoxTK2xYC+baDT21nlLPIvrQWMMRfxYZeoNAO0Ac8S5hubQz2XMedU7Jz7amfyungRY5i0F+5DyC+CIQxiPE4cS6ITu2bQkBnorHcXhQhByxY0xbFKFrnIj7w2ckyAUbAg4AvfTlrKS6eOjRa/e5ZA0zYuSvlImCO1iF8IB5GwKrUqEn2VG8qns1qs8JEFLYjbEsrFuFbEpwAbZ1+WJESlFu17aViIRC24nsVwpKS7NEkxurBNg6jCCjO1rEIBFlSNCAMKqTjPZwkr2nqy7ibC6DHCsIzzizZYgbWtleu9NKpZX8BhQW1YRWhz+5Cf9kKxxE0uQ36aVuU6dyEmLe1zCxIQACH5BAAjAAAALAAAAACCADAAhwAAAAEBAQICAgMDAwQEBAUFBQYGBgcHBwgICAkJCQoKCgsLCwwMDA0NDQ4ODg8PDxAQEBERERISEhMTExQUFBUVFRYWFhcXFxgYGBkZGRoaGhsbGxwcHB0dHR4eHh8fHyAgICEhISIiIiMjIyQkJCUlJSYmJicnJygoKCkpKSoqKisrKywsLC0tLS4uLi8vLzAwMDExMTIyMjMzMzQ0NDU1NTY2Njc3Nzg4ODk5OTo6Ojs7Ozw8PD09PT4+Pj8/P0BAQEFBQUJCQkNDQ0REREVFRUZGRkdHR0hISElJSUpKSktLS0xMTE1NTU5OTk9PT1BQUFpSUmRVVW5XV3dZWX9bW4hdXZBfX5dgYJ1iYqRjY6lkZK9lZbRmZrlmZr1nZ8FnZ8RoaMdoaMppacxpac5pac9padFpac9tZs1wZMtzYcl2Xsd5XMR8WsJ+WMCAVr6CVb2EU7uFUrqHUbmIULiJT7eKT7eKTraLTrWLTrWLT7SLT66JYaiIcqKHgp2FkZiEn5ODq4+Ct46GyY6J15CM4pGP6ZOS75WU85aV9ZeW95eX+JiY+JeX+JeX95aW9ZWV85OT75GR6o+P5I6O24yM0IuLw42NvJCQtJOTrJeXo5ubm5ycnJ2dnZ6enp+fn6CgoKGhoaKioqOjo6SkpKWlpaeoqaqsrqyvsq6ytrC1ubK3vbS6wLW8wra+xbi/x7nBybnCy7rEzbvFzrvG0LzG0bzH0rzI1L3J1b3J1r3K1s3W4OHn7fDz9vj5+/z9/f7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v7+/v///wj+AP8JHEiwoMGD/3hRqsSQ0q5fCCNKnEixosWLGCtKqiSp179euxbyykiypMmTKAXyquSx4C6WKWPKnGmSkiSEkyjR3MmzJ0GYB3tV8km0KMqhEZEaXcp0IlCDQptKnUrQIU6dVLMyDRm00kiJELWKNfmr0q6CQm+CxRN2rEVfbldOGtlrEseKeL66pcjrDNyxvuw21Dtx15q9FHudOYu4ZC883xoj9HWGVcpdrWbN2rWLVy+If4vmlWzw26ZRMVNhWc16dRYrV/bkgdNG1ebOoU9qJl3Q1J+2J3e14TMnT549e7K0Xr4c+Zw+rTq3rMgLD++Br/wa9dULpCpAf/r+8MFzXDlz18jh9IGFuzSe6ZJ3nYHflBejQ4y+/uLFa5aq8cmdh556s3i2BmOS9UUYVb0wsggjD03Gyy6w/FfeeVrsMUcq7aXUw4cghvhhRIrNgtg3u9zHCH0S9dLfKnzkYV5rWWgYXW4XiahjDwj94ocqvNmH34IX+YLHGX3QEWBrWtDRR2cW7SjiQaZ5cp1AvjCiJSM4VmTYQL70l+Qey2mRRx8FAkcQiBid4oea16WIH4stQoYQSKzwQSaNZ0Y4EJsWzTIfT2cUumChiBbUV6EFteKofYZQcoorr+AFBx6jIVQdpnNo0Vwbu/gCKEXyEabIIYcgaNAuqC4SkSj+hbZCkGKIDopdoaE06ugvtbRiya+WdG
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  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
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  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"}}
2025-09-01 18:33:31.078  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (15141-byte body)
2025-09-01 18:33:31.172  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=16.93ms min=3.32ms max=59.56ms count=58
2025-09-01 18:33:31.173  8985-8985  AssistStructure         com.example.repairorderapp           I  Flattened final assist data: 3776 bytes, containing 1 windows, 17 views
2025-09-01 18:33:31.177  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码原始响应长度: 15141
2025-09-01 18:33:31.178  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码token: 1ae86997-f10d-44d9-8157-cd24a535f214
2025-09-01 18:33:31.179  8985-8985  LoginActivity           com.example.repairorderapp           D  Base64字符串前20个字符: R0lGODlhggAwAPcAAAAA
2025-09-01 18:33:31.179  8985-8985  LoginActivity           com.example.repairorderapp           D  完整Base64长度: 15024
2025-09-01 18:33:31.181  8985-8985  LoginActivity           com.example.repairorderapp           D  解码后字节数: 11266
2025-09-01 18:33:31.184  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码图片解码成功: 130x48
2025-09-01 18:33:31.238  8985-9066  ServerTest              com.example.repairorderapp           D  服务器连接测试1结果: 200
2025-09-01 18:33:31.238  8985-9066  ServerTest              com.example.repairorderapp           D  开始测试连接: https://plat.sczjzy.com.cn/api/magina/anno/captcha
2025-09-01 18:33:31.239  8985-9066  TrafficStats            com.example.repairorderapp           D  tagSocket(136) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:31.438  8985-9066  ServerTest              com.example.repairorderapp           D  服务器连接测试2结果: 200
2025-09-01 18:33:31.723  8985-8985  VRI[SplashActivity]     com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:31.724  8985-8985  VRI[SplashActivity]     com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:31.748  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity停止: SplashActivity
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  设置启动页面状态: false
2025-09-01 18:33:31.751  8985-8985  TokenManager            com.example.repairorderapp           D  会话过期对话框引用为空，已清理
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity销毁: SplashActivity
2025-09-01 18:33:31.751  8985-8985  RepairOrderApp          com.example.repairorderapp           D  SplashActivity销毁，关闭TokenManager对话框
2025-09-01 18:33:31.751  8985-8985  TokenManager            com.example.repairorderapp           D  会话过期对话框引用为空，已清理
2025-09-01 18:33:31.752  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@23fe54a
2025-09-01 18:33:31.760  8985-8985  WindowManager           com.example.repairorderapp           E  android.view.WindowLeaked: Activity com.example.repairorderapp.ui.launch.SplashActivity has leaked window com.android.internal.policy.DecorView{df406aa V.E...... R.....ID 0,0-1024,504}[SplashActivity] that was originally added here (Ask Gemini)
at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1246)
at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1232)
at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:428)
at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
at android.app.Dialog.show(Dialog.java:352)
at com.example.repairorderapp.network.TokenInterceptor.showTokenExpiredDialog(TokenInterceptor.kt:299)
at com.example.repairorderapp.network.TokenInterceptor.handleTokenExpiredInOkHttp$lambda$2(TokenInterceptor.kt:246)
at com.example.repairorderapp.network.TokenInterceptor.$r8$lambda$E9gcnPczV6BxM7YxsaXthat04hQ(Unknown Source:0)
at com.example.repairorderapp.network.TokenInterceptor$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at android.os.Handler.handleCallback(Handler.java:959)
at android.os.Handler.dispatchMessage(Handler.java:100)
at android.os.Looper.loopOnce(Looper.java:232)
at android.os.Looper.loop(Looper.java:317)
at android.app.ActivityThread.main(ActivityThread.java:8705)
at java.lang.reflect.Method.invoke(Native Method)
at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:886)
2025-09-01 18:33:31.762  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@ea86b4c
2025-09-01 18:33:33.659  8985-9081  ProfileInstaller        com.example.repairorderapp           D  Installing profile for com.example.repairorderapp
2025-09-01 18:33:34.395  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:53dfdf2c: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser true
2025-09-01 18:33:34.396  8985-8985  InputMethodManager      com.example.repairorderapp           D  showSoftInput() view=androidx.appcompat.widget.AppCompatEditText{2cf3267 VFED..CL. .F.P..ID 79,1189-665,1333 #7f090233 app:id/et_captcha aid=1073741826} flags=0 reason=SHOW_SOFT_INPUT
2025-09-01 18:33:34.407  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=252.17ms min=13.34ms max=2937.96ms count=13
2025-09-01 18:33:35.153  8985-8985  InsetsController        com.example.repairorderapp           D  show(ime(), fromIme=true)
2025-09-01 18:33:35.163  8985-9087  InteractionJankMonitor  com.example.repairorderapp           W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.example.repairorderapp
2025-09-01 18:33:35.368  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:53dfdf2c: onShown
2025-09-01 18:33:35.902  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=498.06ms min=384.39ms max=612.61ms count=3
2025-09-01 18:33:37.401  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=499.59ms min=498.43ms max=500.49ms count=3
2025-09-01 18:33:37.823  8985-8990  .repairorderapp         com.example.repairorderapp           W  Cleared Reference was only reachable from finalizer (only reported once)
2025-09-01 18:33:37.865  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 7170KB AllocSpace bytes, 37(840KB) LOS objects, 49% free, 5732KB/11MB, paused 729us,6.476ms total 64.151ms
2025-09-01 18:33:38.834  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=477.60ms min=429.24ms max=514.22ms count=3
2025-09-01 18:33:40.217  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=276.56ms min=65.09ms max=499.30ms count=5
2025-09-01 18:33:41.484  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=422.33ms min=267.39ms max=500.27ms count=3
2025-09-01 18:33:42.542  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=352.57ms min=58.70ms max=499.65ms count=3
2025-09-01 18:33:43.023  8985-8985  LoginActivity           com.example.repairorderapp           D  开始获取加密密钥...
2025-09-01 18:33:43.024  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getEncryptionKey
2025-09-01 18:33:43.025  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 0
2025-09-01 18:33:43.026  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (0-byte body)
2025-09-01 18:33:43.026  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/anno/key
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.026  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/anno/key (59ms)
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:01 GMT
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.085  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"first":"453de329-4593-47fc-bcc0-f5df154f7e33","second":"04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b"}}
2025-09-01 18:33:43.086  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (225-byte body)
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  密钥响应: {"code":200,"message":"ok","data":{"first":"453de329-4593-47fc-bcc0-f5df154f7e33","second":"04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b"}}
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  密钥token: 453de329-4593-47fc-bcc0-f5df154f7e33
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  公钥: 04801f7b8c90804962e1b9a99ede8479d3fd1adcf0733dc8fe35064ac835c55b5957e5e7f55e02430b1b3ee7fdc45dbd9cc31722b9fccf8c7eb2a9a39974847c2b
2025-09-01 18:33:43.086  8985-8985  LoginActivity           com.example.repairorderapp           D  开始登录流程...
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  用户名: B0000003
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码: wry5v
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  验证码token: 1ae86997-f10d-44d9-8157-cd24a535f214
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  密码token: 453de329-4593-47fc-bcc0-f5df154f7e33
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  开始使用SM2加密密码...
2025-09-01 18:33:43.087  8985-8985  LoginActivity           com.example.repairorderapp           D  处理后的公钥长度: 130, 公钥前10个字符: 04801f7b8c
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  加密前数据长度: 10
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  格式化后的公钥前10个字符: 04801f7b8c
2025-09-01 18:33:43.282  8985-8985  LoginActivity           com.example.repairorderapp           D  创建SM2曲线参数
2025-09-01 18:33:43.305  8985-8985  LoginActivity           com.example.repairorderapp           D  解码公钥
2025-09-01 18:33:43.306  8985-8985  LoginActivity           com.example.repairorderapp           D  公钥字节长度: 65
2025-09-01 18:33:43.307  8985-8985  LoginActivity           com.example.repairorderapp           D  创建SM2加密引擎
2025-09-01 18:33:43.320  8985-8985  LoginActivity           com.example.repairorderapp           D  执行SM2加密
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  加密完成，密文长度: 107
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  密码加密完成，加密后长度: 214, 加密后前20个字符: 049e0d9656a61e0719ad
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  密码加密完成
2025-09-01 18:33:43.369  8985-8985  LoginActivity           com.example.repairorderapp           D  发送登录请求...
2025-09-01 18:33:43.374  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_login
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:43.377  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 374
2025-09-01 18:33:43.378  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"password":"049e0d9656a61e0719ad61c24baeab37a72f735f1d78323c287b2e55310c888e8db3f72d2b7cc1b8a2f8730ec21d4f6d5b9e657757daf5a835251bcd56da575acdfdb72f110d66542b965c7a00ab997ad530dc648b1fd6ceb380adbf38d08e4156aba99d4c6cacb22993ec","code":"B0000003","passwordToken":"453de329-4593-47fc-bcc0-f5df154f7e33","captcha":"wry5v","captchaToken":"1ae86997-f10d-44d9-8157-cd24a535f214"}
2025-09-01 18:33:43.386  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (374-byte body)
2025-09-01 18:33:43.387  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.388  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/wechat/staff/app/login
2025-09-01 18:33:43.388  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=空, userId=
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           E  令牌为空，所有存储位置都为空
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           E  token_pref中的所有键值: {}
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 空
2025-09-01 18:33:43.389  8985-9047  TokenInterceptor        com.example.repairorderapp           W  请求未添加令牌，可能导致认证失败
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.390  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.406  8985-8990  .repairorderapp         com.example.repairorderapp           I  Background concurrent mark compact GC freed 6617KB AllocSpace bytes, 0(0B) LOS objects, 49% free, 6044KB/11MB, paused 1.198ms,7.134ms total 44.384ms
2025-09-01 18:33:43.553  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=14.11ms min=2.15ms max=51.62ms count=55
2025-09-01 18:33:43.572  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/wechat/staff/app/login (185ms)
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-09-01 18:33:43.573  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (177-byte body)
2025-09-01 18:33:43.574  8985-8985  LoginActivity           com.example.repairorderapp           D  登录响应: {"code":200,"message":"ok","data":{"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}}
2025-09-01 18:33:43.574  8985-8985  LoginActivity           com.example.repairorderapp           D  登录成功
2025-09-01 18:33:43.574  8985-8985  TokenInterceptor        com.example.repairorderapp           D  Token过期状态已重置，开始登录保护期
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  保存令牌信息:
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userId: 1730205532934926338
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userCode: B0000003
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - userName: 苏应来
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  - engineerId: 1730205532934926338
2025-09-01 18:33:43.583  8985-8985  LoginActivity           com.example.repairorderapp           D  保存Token信息，不设置本地过期时间
2025-09-01 18:33:43.584  8985-8985  LoginActivity           com.example.repairorderapp           D  服务器响应数据: {"isSubscribed":false,"token":"19c9a4e1-9af0-42b6-9024-20fffd99c636","user":{"id":"1730205532934926338","code":"B0000003","name":"苏应来"}}
2025-09-01 18:33:43.590  8985-8985  LoginActivity           com.example.repairorderapp           D  令牌保存验证: 成功
2025-09-01 18:33:43.593  8985-8985  LoginActivity           com.example.repairorderapp           D  保存登录凭据 - 用户名: B0000003, 记住密码: true
2025-09-01 18:33:43.593  8985-8985  LoginActivity           com.example.repairorderapp           D  登录令牌保存成功
2025-09-01 18:33:43.596  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: LoginService_getResources
2025-09-01 18:33:43.597  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.598  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.598  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.600  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/magina/system/resources
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.601  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.602  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.602  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.603  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.603  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.604  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.604  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.605  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.605  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.613  8985-9016  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getCosBucketInfo
2025-09-01 18:33:43.615  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.618  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.618  8985-9091  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/cos/bucket
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.619  8985-9091  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.620  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/magina/system/resources (77ms)
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.675  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.676  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":[{"label":"工单管理","value":"/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"/disassembly","type":{"value":"menu","label":"菜单"}}]}
2025-09-01 18:33:43.676  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (3722-byte body)
2025-09-01 18:33:43.677  8985-8985  LoginActivity           com.example.repairorderapp           D  获取到权限数据: 23 项
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/cos/bucket (66ms)
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.685  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"bucket":"sczjzy-1332842668","region":"ap-chengdu","prefix":"prod/"}}
2025-09-01 18:33:43.686  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (104-byte body)
2025-09-01 18:33:43.693  8985-9016  PermissionManager       com.example.repairorderapp           D  开始异步初始化权限...
2025-09-01 18:33:43.693  8985-8985  LoginActivity           com.example.repairorderapp           D  设备信息上传前Token验证: 可用
2025-09-01 18:33:43.693  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:43.694  8985-8985  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:43.696  8985-9016  PermissionManager       com.example.repairorderapp           D  权限初始化完成，共 23 项权限
2025-09-01 18:33:43.794  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:43.794  8985-8985  PermissionManager       com.example.repairorderapp           D  通知权限变更，观察者数量: 0
2025-09-01 18:33:43.803  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-09-01 18:33:43.807  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-09-01 18:33:43.808  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始收集完整设备信息...
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  当前用户信息: userId=1730205532934926338, userCode=B0000003, userName=苏应来
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  开始获取友好设备型号名称 - 品牌: google, 原始型号: sdk_gphone64_x86_64
2025-09-01 18:33:43.809  8985-8985  DeviceInfoCollector     com.example.repairorderapp           I  设备型号名称获取完成 - 原始: sdk_gphone64_x86_64 -> 友好: sdk_gphone64_x86_64
2025-09-01 18:33:43.868  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.870  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.874  8985-8985  DeviceInfoCollector     com.example.repairorderapp           D  完整设备信息收集完成: google sdk_gphone64_x86_64 (Android Android 15)
2025-09-01 18:33:43.875  8985-8985  LoginActivity           com.example.repairorderapp           I  登录成功，远程配置更新已触发
2025-09-01 18:33:43.875  8985-8985  LoginActivity           com.example.repairorderapp           D  权限加载完成，正在跳转到主页
2025-09-01 18:33:43.876  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  🚀 登录成功，开始更新远程配置...
2025-09-01 18:33:43.877  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  🚀 开始请求配置: userId=1730205532934926338, deviceId=cf7f6ce27817ef1a, appVersion=1.0.3-debug, hasToken=true
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-09-01 18:33:43.886  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-User-Id: 1730205532934926338
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Device-Id: cf7f6ce27817ef1a
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  X-App-Version: 1.0.3-debug
2025-09-01 18:33:43.890  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:43.890  8985-9091  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.891  8985-9091  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-User-Id: 1730205532934926338
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Device-Id: cf7f6ce27817ef1a
2025-09-01 18:33:43.892  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-App-Version: 1.0.3-debug
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.893  8985-9091  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.894  8985-8985  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Ljava/lang/Void;-><init>()V (unsupported, reflection, allowed)
2025-09-01 18:33:43.898  8985-9012  beacon                  com.example.repairorderapp           I  logAble: false , SDKVersion: 4.2.86.12-external
2025-09-01 18:33:43.900  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.900  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:43.901  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1051
2025-09-01 18:33:43.904  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1556140032,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756722823793}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-09-01 10:33:43","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-09-01 10:33:43","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"GMT","totalMemory":2067398656,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-09-01 18:33:43.905  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1051-byte body)
2025-09-01 18:33:43.905  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.905  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:43.906  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.907  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           E  BeaconSDK init success! appkey is: 0AND05KOZX0E3L2H, packageName is: com.example.repairorderapp
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.907  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9012  beacon                  com.example.repairorderapp           I  beacon logAble: false
2025-09-01 18:33:43.908  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:43.920  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务已创建
2025-09-01 18:33:43.923  8985-8985  LocationUpdateService   com.example.repairorderapp           W  后台位置权限状态变化: false -> true
2025-09-01 18:33:43.925  8985-8985  LocationUpdateService   com.example.repairorderapp           I  权限状态检查 - 后台位置: true, 后台限制: false, 精确位置: true
2025-09-01 18:33:43.927  8985-8985  LocationUpdateService   com.example.repairorderapp           D  屏幕状态监听器注册成功
2025-09-01 18:33:43.928  8985-8985  LocationUpdateService   com.example.repairorderapp           D  检查用户信息: userId=1730205532934926338, userName=苏应来
2025-09-01 18:33:43.930  8985-9116  .repairorderapp         com.example.repairorderapp           W  Accessing hidden method Landroid/app/ActivityThread;->currentProcessName()Ljava/lang/String; (unsupported, reflection, allowed)
2025-09-01 18:33:43.942  8985-8985  定位调试                    com.example.repairorderapp           I  位置服务 - 成功设置隐私合规接口：已同意
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/config/get
2025-09-01 18:33:43.946  8985-9091  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/config/get (剩余保护时间: 29秒)
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/config/get (57ms)
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:43.948  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:43.951  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 9d4124a209e24147a2e94b4681fc1aa6
2025-09-01 18:33:43.954  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.955  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:43.955  8985-9091  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:43.957  8985-9016  RemoteConfigManager     com.example.repairorderapp           I  ✅ API调用成功: code=200
2025-09-01 18:33:43.958  8985-9016  RemoteConfigManager     com.example.repairorderapp           W  ❌ API响应数据为空: success=false, data=false
2025-09-01 18:33:43.958  8985-9016  RemoteConfigManager     com.example.repairorderapp           W  ⚠️ 登录后配置更新失败，将使用本地缓存配置
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:43.972  8985-9047  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-09-01 18:33:43.973  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (68ms)
2025-09-01 18:33:43.973  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:02 GMT
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:43.974  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:43.975  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 7b83fbb299144994beb84468a6b36f9e
2025-09-01 18:33:43.975  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:43.976  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:43.977  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:43.993  8985-9012  RepairOrderApp          com.example.repairorderapp           I  COS SDK 初始化成功: Region=ap-chengdu, Bucket=sczjzy-1332842668
2025-09-01 18:33:44.047  8985-9117  TrafficStats            com.example.repairorderapp           D  tagSocket(186) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.068  8985-8992  System                  com.example.repairorderapp           W  A resource failed to call close.
2025-09-01 18:33:44.112  8985-8985  nativeloader            com.example.repairorderapp           D  Load /data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk!/lib/x86_64/libtencentloc.so using ns clns-7 from class loader (caller=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk): ok
2025-09-01 18:33:44.116  8985-8985  nativeloader            com.example.repairorderapp           D  Load libjnirtk.so using ns clns-7 from class loader (caller=/data/app/~~j6WKVkU8e8TOfTBLWqj3Ow==/com.example.repairorderapp-IU4NfX5mQLNXiiAdsrTZLg==/base.apk): dlopen failed: library "libjnirtk.so" not found
2025-09-01 18:33:44.126  8985-9116  TrafficStats            com.example.repairorderapp           D  tagSocket(177) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.128  8985-9115  TrafficStats            com.example.repairorderapp           D  tagSocket(188) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  位置请求配置成功
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  初始化位置服务配置
2025-09-01 18:33:44.158  8985-8985  LocationUpdateService   com.example.repairorderapp           D  定位服务状态: 可用
2025-09-01 18:33:44.160  8985-8985  LocationUpdateService   com.example.repairorderapp           D  注册网络状态监听
2025-09-01 18:33:44.161  8985-8985  LocationUpdateService   com.example.repairorderapp           D  当前网络状态: 可用
2025-09-01 18:33:44.167  8985-8985  LocationUpdateService   com.example.repairorderapp           D  网络状态监听注册成功
2025-09-01 18:33:44.170  8985-8985  LocationUpdateService   com.example.repairorderapp           D  设备省电模式状态: false
2025-09-01 18:33:44.171  8985-8985  LocationUpdateService   com.example.repairorderapp           I  日志系统初始化成功
2025-09-01 18:33:44.171  8985-9141  LocationUpdateService   com.example.repairorderapp           D  网络连接恢复
2025-09-01 18:33:44.174  8985-9012  LocationUpdateService   com.example.repairorderapp           D  没有缓存的位置数据需要上传
2025-09-01 18:33:44.180  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           D  电池优化状态检查: 应用包名=com.example.repairorderapp, 已忽略电池优化=false
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           W  应用未在电池优化白名单中，可能影响后台定位服务的稳定性
2025-09-01 18:33:44.182  8985-8985  LocationUpdateService   com.example.repairorderapp           W  建议用户在设置中将应用加入电池优化白名单
2025-09-01 18:33:44.197  8985-8985  LocationUpdateService   com.example.repairorderapp           D  已显示电池优化提醒通知
2025-09-01 18:33:44.199  8985-8985  LocationUpdateService   com.example.repairorderapp           D  已获取唤醒锁(正常模式)，5分钟后自动释放
2025-09-01 18:33:44.200  8985-8985  LocationUpdateService   com.example.repairorderapp           D  屏幕状态监听器已注册，跳过重复注册
2025-09-01 18:33:44.200  8985-8985  LocationUpdateService   com.example.repairorderapp           D  网络回调已注册，跳过重复注册
2025-09-01 18:33:44.201  8985-8985  LocationUpdateService   com.example.repairorderapp           D  省电模式监听器注册成功
2025-09-01 18:33:44.202  8985-8985  LocationUpdateService   com.example.repairorderapp           D  位置请求配置已更新: GPS=true, 精度=3, 间隔=300000ms
2025-09-01 18:33:44.205  8985-9016  LocationUpdateService   com.example.repairorderapp           I  权限状态 - 精确位置: true, 粗略位置: true
2025-09-01 18:33:44.205  8985-8985  UploadWorker            com.example.repairorderapp           D  已调度周期性位置上传任务。
2025-09-01 18:33:44.205  8985-9016  LocationUpdateService   com.example.repairorderapp           I  位置请求配置: interval=300000, level=3
2025-09-01 18:33:44.206  8985-9016  LocationUpdateService   com.example.repairorderapp           D  请求位置更新 (正常模式-5分钟间隔)
2025-09-01 18:33:44.206  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务已启动
2025-09-01 18:33:44.206  8985-9012  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:33:44.207  8985-9012  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 距离上次更新已经过去 1756722824 秒
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           I  用户登录后上传设备信息
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  设备信息: userId='1730205532934926338', userCode='B0000003', userName='苏应来'
2025-09-01 18:33:44.207  8985-8985  DeviceDataUploadManager com.example.repairorderapp           D  权限信息: {"LOCATION":true,"BACKGROUND_LOCATION":true,"CAMERA":false,"STORAGE":false,"NOTIFICATION":true,"NETWORK_STATE":true,"GPS_ENABLED":true,"NETWORK_LOCATION_ENABLED":true}
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  位置请求正在进行中，跳过重复请求
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  当前处于正常模式，使用5分钟更新间隔
2025-09-01 18:33:44.208  8985-9012  LocationUpdateService   com.example.repairorderapp           D  定时位置更新: 等待 299 秒后进行下次更新
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> POST https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Type: application/json; charset=UTF-8
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  Content-Length: 1051
2025-09-01 18:33:44.210  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"appVersion":"1.0.3-debug","availableStorage":1556140032,"brand":"google","collectCount":1,"cpuAbi":"x86_64","currentConfigDetails":"{\"configId\":1,\"configName\":\"default\",\"logLevel\":\"INFO\",\"enableLocationLog\":true,\"locationLogInterval\":3000,\"logUploadInterval\":3600,\"maxLogFiles\":5,\"collectTime\":1756722823873}","currentConfigVersion":"1.0.0","deviceId":"cf7f6ce27817ef1a","firstCollectTime":"2025-09-01 10:33:43","isEmulator":false,"isRooted":false,"language":"en_US","lastUpdateTime":"2025-09-01 10:33:43","manufacturer":"Android 15 (API 35)","model":"sdk_gphone64_x86_64","networkType":"WiFi","osType":"Android","osVersion":"Android 15","permissionsInfo":"{\"LOCATION\":true,\"BACKGROUND_LOCATION\":true,\"CAMERA\":false,\"STORAGE\":false,\"NOTIFICATION\":true,\"NETWORK_STATE\":true,\"GPS_ENABLED\":true,\"NETWORK_LOCATION_ENABLED\":true}","screenDensity":2.625,"screenResolution":"1080x2400","sdkVersion":35,"timeZone":"GMT","totalMemory":2067398656,"userCode":"B0000003","userId":"1730205532934926338","userName":"苏应来"}
2025-09-01 18:33:44.211  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END POST (1051-byte body)
2025-09-01 18:33:44.211  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity暂停: LoginActivity
2025-09-01 18:33:44.211  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.211  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:44.212  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.218  8985-8985  LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-09-01 18:33:44.219  8985-8985  DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-09-01 18:33:44.239  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity创建: MainActivity
2025-09-01 18:33:44.244  8985-8985  RepairOrderApp          com.example.repairorderapp           D  为 MainActivity 设置全局触摸监听
2025-09-01 18:33:44.270  8985-9047  TokenInterceptor        com.example.repairorderapp           W  检测到Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.271  8985-9047  TokenInterceptor        com.example.repairorderapp           W  在OkHttp层面处理Token过期: https://plat.sczjzy.com.cn/api/logcontrol/device/upload
2025-09-01 18:33:44.271  8985-9047  TokenInterceptor        com.example.repairorderapp           W  登录保护期内，跳过Token过期处理: https://plat.sczjzy.com.cn/api/logcontrol/device/upload (剩余保护时间: 29秒)
2025-09-01 18:33:44.272  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/logcontrol/device/upload (60ms)
2025-09-01 18:33:44.274  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:44.275  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:03 GMT
2025-09-01 18:33:44.275  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json;charset=UTF-8
2025-09-01 18:33:44.276  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-length: 37
2025-09-01 18:33:44.276  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  x-trace-id: 353014795e0f4426bfb20d6b08c75ddd
2025-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":401,"message":"会话过期"}
2025-09-01 18:33:44.277  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (37-byte body)
2025-09-01 18:33:44.315  8985-9120  BluetoothAdapter        com.example.repairorderapp           D  isLeEnabled(): ON
2025-09-01 18:33:44.368  8985-9016  LocationUpdateService   com.example.repairorderapp           D  位置更新请求成功，等待回调
2025-09-01 18:33:44.372  8985-9011  LocationUpdateService   com.example.repairorderapp           D  没有缓存的位置数据需要上传
2025-09-01 18:33:44.374  8985-9012  LocationUpdateService   com.example.repairorderapp           D  保活任务已取消
2025-09-01 18:33:44.379  8985-9016  UploadWorker            com.example.repairorderapp           D  已调度周期性位置上传任务。
2025-09-01 18:33:44.382  8985-9016  定位调试                    com.example.repairorderapp           I  位置更新服务已启动
2025-09-01 18:33:44.513  8985-8985  PermissionManager       com.example.repairorderapp           D  原始权限JSON数据: [{"label":"工单管理","value":"\/workPool","type":{"value":"menu","label":"菜单"}},{"label":"工程师仓库","value":"\/engineerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"工程师管理","value":"\/engineerManagement","type":{"value":"menu","label":"菜单"}},{"label":"客户仓库","value":"\/customerWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"时效统计","value":"\/statistics","type":{"value":"menu","label":"菜单"}},{"label":"客评价","value":"\/evaluation","type":{"value":"menu","label":"菜单"}},{"label":"待接工单","value":"\/pendingOrder","type":{"value":"menu","label":"菜单"}},{"label":"我的工单","value":"\/myWorkOrder","type":{"value":"menu","label":"菜单"}},{"label":"地图位置","value":"\/map","type":{"value":"menu","label":"菜单"}},{"label":"申诉工单","value":"\/appealOrder","type":{"value":"menu","label":"菜单"}},{"label":"知识库","value":"\/engLearn","type":{"value":"menu","label":"菜单"}},{"label":"个人仓库","value":"\/wareStore","type":{"value":"menu","label":"菜单"}},{"label":"报销单","value":"\/bill","type":{"value":"menu","label":"菜单"}},{"label":"耗材仓库","value":"\/warehouse","type":{"value":"menu","label":"菜单"}},{"label":"机器仓库","value":"\/machineWarehouse","type":{"value":"menu","label":"菜单"}},{"label":"客户管理","value":"\/customer","type":{"value":"menu","label":"菜单"},"children":[{"label":"基础信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:base"},{"label":"员工信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:staff"},{"label":"商务信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:business"},{"label":"机器信息","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:machine"},{"label":"联网设置","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:iot"},{"label":"用户标签","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:tag"},{"label":"拜访记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:visit"},{"label":"购买意向","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:buy"},{"label":"积分记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:integral"},{"label":"合约记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:agreement"},{"label":"访问记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:interview"},{"label":"搜索记录","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:search"},{"label":"耗材仓库","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:store"},{"label":"客户价值","value":"","type":{"value":"button","label":"按钮"},"permit":"@ums:manage:customer:cost"}]},{"label":"问题商品","value":"\/wrong","type":{"value":"menu","label":"菜单"}},{"label":"采购申请","value":"\/partApply","type":{"value":"menu","label":"菜单"}},{"label":"申领耗材","value":"\/wareApply","type":{"value":"menu","label":"菜单"}},{"label":"申请退料","value":"\/returnApply","type":{"value":"menu","label":"菜单"}},{"label":"毛机维修","value":"\/imperfect","type":{"value":"menu","label":"菜单"}},{"label":"翻新组件","value":"\/partRepair","type":{"value":"menu","label":"菜单"}},{"label":"机器拆机","value":"\/disassembly","type":{"value":"menu","label":"菜单"}}]
2025-09-01 18:33:44.540  8985-8985  MainActivity            com.example.repairorderapp           D  用户已登录，启动位置服务
2025-09-01 18:33:44.541  8985-8985  MainActivity            com.example.repairorderapp           D  位置服务状态:
位置服务开关: ✓ 启用
服务运行状态: ✓ 运行中
配置文件状态: ✓ 正常
配置项数量: 1
location_service_enabled = true
基础位置权限: ✓
后台位置权限: ✓
系统定位服务: ✓
2025-09-01 18:33:44.541  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  正在启动位置服务...
2025-09-01 18:33:44.542  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  已发送前台服务启动命令
2025-09-01 18:33:44.543  8985-8985  LocationUpdateWorker    com.example.repairorderapp           D  已调度WorkManager定期位置更新任务
2025-09-01 18:33:44.543  8985-8985  MainActivity            com.example.repairorderapp           D  应用启动完成 - 调试模式
2025-09-01 18:33:44.543  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: page_load - 耗时: 294ms [耗时: 294ms] [内存: 9406KB]
2025-09-01 18:33:44.545  8985-8985  MainActivity_onCreate   com.example.repairorderapp           I  内存快照 - 使用: 9MB/192MB (4%) [内存: 9439KB]
2025-09-01 18:33:44.547  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity开始: MainActivity
2025-09-01 18:33:44.625  8985-8985  PermissionManager       com.example.repairorderapp           D  注册权限观察者，当前观察者数量: 1
2025-09-01 18:33:44.625  8985-8985  ProfileFragment         com.example.repairorderapp           D  权限已预加载，直接初始化功能按钮
2025-09-01 18:33:44.664  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  OnBackInvokedCallback is not enabled for the application.
Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-09-01 18:33:44.666  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity恢复: MainActivity
2025-09-01 18:33:44.669  8985-8985  GlobalRetrofitProxy     com.example.repairorderapp           D  代理执行: WorkOrderApi_getWorkOrderCount
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  X-Auth-Token: 19c9a4e1-9af0-42b6-9024-20fffd99c636
2025-09-01 18:33:44.670  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:44.670  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:44.670  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:44.671  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:44.678  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=281.13ms min=16.79ms max=1070.67ms count=4
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/engineer/work-order/sumaryCount (68ms)
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:03 GMT
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:44.739  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:44.740  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"pendingOrdersCount":"0","myWorkOrderCount":"0","appealOrderCount":"0"}}
2025-09-01 18:33:44.740  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (107-byte body)
2025-09-01 18:33:44.867  8985-8985  LogUploadManager        com.example.repairorderapp           W  设备信息上传失败: 200
2025-09-01 18:33:44.867  8985-8985  DeviceDataUploadManager com.example.repairorderapp           W  登录后设备信息上传失败
2025-09-01 18:33:44.868  8985-8985  LoginActivity           com.example.repairorderapp           I  登录成功，设备信息上传已触发
2025-09-01 18:33:44.873  8985-8985  LocationUpdateService   com.example.repairorderapp           I  位置服务前台服务已启动
2025-09-01 18:33:44.894  8985-8985  ProfileFragment         com.example.repairorderapp           D  获取工单数量成功：待接工单=0, 我的工单=0, 申诉单=0
2025-09-01 18:33:44.908  8985-9120  CompatChangeReporter    com.example.repairorderapp           D  Compat change id reported: 247079863; UID 10228; state: ENABLED
2025-09-01 18:33:45.053  8985-8985  InsetsController        com.example.repairorderapp           D  hide(ime(), fromIme=true)
2025-09-01 18:33:45.068  8985-8985  RemoteInpu...ectionImpl com.example.repairorderapp           W  requestCursorUpdates on inactive InputConnection
2025-09-01 18:33:45.069  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:9d4d5339: onCancelled at PHASE_CLIENT_ANIMATION_CANCEL
2025-09-01 18:33:45.071  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:f3654d09: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-09-01 18:33:45.071  8985-8985  ImeTracker              com.example.repairorderapp           I  com.example.repairorderapp:f3654d09: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-09-01 18:33:45.402  8985-8985  VRI[LoginActivity]      com.example.repairorderapp           D  visibilityChanged oldVisibility=true newVisibility=false
2025-09-01 18:33:45.422  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity停止: LoginActivity
2025-09-01 18:33:45.423  8985-8985  RepairOrderApp          com.example.repairorderapp           D  Activity销毁: LoginActivity
2025-09-01 18:33:45.424  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@20080da
2025-09-01 18:33:46.808  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=142.48ms min=3.28ms max=1640.02ms count=13
2025-09-01 18:33:46.825  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-09-01 18:33:47.038  8985-9143  TrafficStats            com.example.repairorderapp           D  tagSocket(155) with statsTag=0xffffffff, statsUid=-1
2025-09-01 18:33:47.543  8985-8985  LocationServiceStarter  com.example.repairorderapp           I  服务启动检查: 成功
2025-09-01 18:33:47.690  8985-8985  UpdateManager           com.example.repairorderapp           D  开始检查更新...
2025-09-01 18:33:47.693  8985-9016  UpdateRepository        com.example.repairorderapp           D  开始检查更新，当前版本: 1
2025-09-01 18:33:47.694  8985-9016  UpdateRepository        com.example.repairorderapp           D  更新检查参数: versionCode=1, versionName=1.0.3-debug, deviceId=cf7f6ce27817ef1a, userId=1730205532934926338
2025-09-01 18:33:47.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> GET https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.696  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  --> END GET
2025-09-01 18:33:47.697  8985-9047  ApiClient               com.example.repairorderapp           D  发送请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  拦截请求: https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  当前线程: OkHttp https://plat.sczjzy.com.cn/...
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token读取详情: accessToken=长度=36, userId=1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  令牌状态: 长度=36
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加令牌头 X-Auth-Token: 19c9a4e1-9...
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  Token已添加到请求头，等待服务器响应验证
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  添加用户ID头 X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D  请求头信息:
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Content-Type: application/json
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    Accept: application/json
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-Auth-Token: ******
2025-09-01 18:33:47.697  8985-9047  TokenInterceptor        com.example.repairorderapp           D    X-CUSTOMER-ID: 1730205532934926338
2025-09-01 18:33:47.828  8985-8985  LocationUpdateService   com.example.repairorderapp           E  位置更新失败: 错误码=404, 原因=ERROR_SERVER_NOT_LOCATION
2025-09-01 18:33:47.829  8985-8985  PerformanceMonitor      com.example.repairorderapp           I  性能监控: location_acquisition - 耗时: 3622ms [耗时: 3622ms] [内存: 11937KB]
2025-09-01 18:33:47.831  8985-8985  LocationUpdateService   com.example.repairorderapp           W  其他位置错误: 404 - ERROR_SERVER_NOT_LOCATION
2025-09-01 18:33:48.334  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=111.16ms min=1.96ms max=1348.55ms count=13
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- 200 https://plat.sczjzy.com.cn/api/app/update?currentVersionCode=1&currentVersionName=1.0.3-debug&deviceId=cf7f6ce27817ef1a&userId=1730205532934926338 (1638ms)
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  server: nginx
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  date: Tue, 02 Sep 2025 01:29:06 GMT
2025-09-01 18:33:49.335  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  content-type: application/json
2025-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  vary: Accept-Encoding
2025-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  strict-transport-security: max-age=31536000
2025-09-01 18:33:49.336  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  {"code":200,"message":"ok","data":{"hasUpdate":false}}
2025-09-01 18:33:49.337  8985-9047  okhttp.OkHttpClient     com.example.repairorderapp           I  <-- END HTTP (54-byte body)
2025-09-01 18:33:49.339  8985-9016  UpdateRepository        com.example.repairorderapp           D  更新检查成功: hasUpdate=false
2025-09-01 18:33:49.339  8985-9016  UpdateRepository        com.example.repairorderapp           D  当前已是最新版本或无权限更新
2025-09-01 18:33:49.342  8985-8985  UpdateManager           com.example.repairorderapp           D  当前已是最新版本
2025-09-01 18:33:49.342  8985-8985  MainActivity            com.example.repairorderapp           D  当前已是最新版本
2025-09-01 18:33:50.112  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=110.98ms min=14.60ms max=1527.41ms count=16
2025-09-01 18:33:50.118  8985-8985  WindowOnBackDispatcher  com.example.repairorderapp           W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda11@7005ef6
2025-09-01 18:33:50.121  8985-9041  HWUI                    com.example.repairorderapp           D  endAllActiveAnimators on 0x72886ff81420 (RippleDrawable) with handle 0x7286b0473550
2025-09-01 18:33:50.172  8985-9041  EGL_emulation           com.example.repairorderapp           D  app_time_stats: avg=479.24ms min=1.70ms max=3254.67ms count=7
2025-09-01 18:33:58.090  8985-9016  EnhancedLogCollector    com.example.repairorderapp           D  批量保存日志成功: 6条
2025-09-01 18:34:28.745  8985-9009  GlobalExceptionMonitor  com.example.repairorderapp           D  异常统计 - 总计: 0, 致命: 0, 网络: 0, JSON: 0, 协程: 0
