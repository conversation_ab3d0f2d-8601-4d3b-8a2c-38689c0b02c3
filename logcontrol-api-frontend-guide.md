# LogControl API 前端集成指南

## 📋 接口概览

本文档提供 LogControl 模块的核心接口使用说明，包含详细的请求参数、响应格式和示例代码，方便前端开发集成。

**基础路径：** `/logcontrol`

---

## 🔧 核心接口

### 1. 获取日志配置

**接口地址：** `GET /logcontrol/config/get`

**功能说明：** 获取当前激活的日志配置，支持用户、设备优先级配置

**认证方式：** 无需认证（公开接口）

**请求头：**
```http
X-Device-Id: android_device_001     # 设备ID（可选）
X-App-Version: 1.0.0               # 应用版本（可选）
X-User-Id: 101                     # 用户ID（可选）
```

**请求示例：**
```javascript
// 使用 axios
const getLogConfig = async () => {
  try {
    const response = await axios.get('/logcontrol/config/get', {
      headers: {
        'X-Device-Id': 'android_device_001',
        'X-App-Version': '1.0.0',
        'X-User-Id': '101'
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取日志配置失败:', error);
  }
};

// 使用 fetch
fetch('/logcontrol/config/get', {
  method: 'GET',
  headers: {
    'X-Device-Id': 'android_device_001',
    'X-App-Version': '1.0.0',
    'X-User-Id': '101'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "configName": "default_config",
    "logLevel": "INFO",
    "enableLocationLog": true,
    "locationLogInterval": 30000,
    "logUploadInterval": 60000,
    "maxLogFiles": 10,
    "configVersion": "1.0.1",
    "isActive": true,
    "createdAt": "2025-01-21T10:00:00",
    "updatedAt": "2025-01-21T10:00:00"
  }
}
```

**配置优先级：** 用户配置 > 设备配置 > 默认配置

---

### 2. 上传设备信息

**接口地址：** `POST /logcontrol/device/upload`

**功能说明：** 上传设备信息，建立设备与用户的关联关系

**认证方式：** 需要 x-auth-token 认证

**请求头：**
```http
Content-Type: application/json
x-auth-token: your_auth_token_here    # 必需
```

**请求体参数：**
```typescript
interface DeviceUploadRequest {
  deviceId: string;          // 设备唯一标识（必需）
  userId?: string;           // 用户ID（可选，String类型避免精度问题）
  userCode?: string;         // 用户编码（可选）
  userName?: string;         // 用户姓名（可选）
  brand: string;             // 手机品牌（必需）
  model: string;             // 手机型号（必需）
  manufacturer?: string;     // 制造商（可选）
  osVersion?: string;        // 系统版本（可选）
  appVersion?: string;       // 应用版本（可选）
  totalMemory?: number;      // 总内存（可选）
  availableMemory?: number;  // 可用内存（可选）
  isRooted?: boolean;        // 是否Root（可选）
  screenWidth?: number;      // 屏幕宽度（可选）
  screenHeight?: number;     // 屏幕高度（可选）
  screenDensity?: number;    // 屏幕密度（可选）
  cpuInfo?: string;          // CPU信息（可选）
  networkType?: string;      // 网络类型（可选）
  language?: string;         // 系统语言（可选）
  timeZone?: string;         // 时区（可选）
}
```

**请求示例：**
```javascript
// 使用 axios
const uploadDeviceInfo = async (deviceInfo) => {
  try {
    const response = await axios.post('/logcontrol/device/upload', deviceInfo, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': localStorage.getItem('authToken')
      }
    });
    return response.data;
  } catch (error) {
    console.error('上传设备信息失败:', error);
  }
};

// 示例数据
const deviceData = {
  deviceId: "android_device_001",
  userId: "101",
  userCode: "sa",
  userName: "超级管理员",
  brand: "Huawei",
  model: "Mate 40 Pro",
  manufacturer: "HUAWEI",
  osVersion: "Android 12",
  appVersion: "1.0.0",
  totalMemory: 8589934592,
  availableMemory: 4294967296,
  isRooted: false,
  screenWidth: 1080,
  screenHeight: 2340,
  screenDensity: 480,
  cpuInfo: "Kirin 9000",
  networkType: "WIFI",
  language: "zh-CN",
  timeZone: "Asia/Shanghai"
};

uploadDeviceInfo(deviceData);
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 🔍 其他配置接口

### 3. 根据版本获取配置

**接口地址：** `GET /logcontrol/config/get-by-version`

**请求参数：**
- `configVersion`: 配置版本号（必需）

**请求示例：**
```javascript
const getConfigByVersion = async (version) => {
  const response = await axios.get('/logcontrol/config/get-by-version', {
    params: { configVersion: version }
  });
  return response.data;
};
```

### 4. 获取配置列表

**接口地址：** `GET /logcontrol/config/list`

**请求示例：**
```javascript
const getAllConfigs = async () => {
  const response = await axios.get('/logcontrol/config/list');
  return response.data;
};
```

### 5. 检查配置更新

**接口地址：** `GET /logcontrol/config/check-updates`

**请求参数：**
- `currentVersion`: 当前版本号（可选）

**请求头：**
- `X-Device-Id`: 设备ID（可选）
- `X-User-Id`: 用户ID（可选）

**请求示例：**
```javascript
const checkConfigUpdates = async (currentVersion) => {
  const response = await axios.get('/logcontrol/config/check-updates', {
    params: { currentVersion },
    headers: {
      'X-Device-Id': 'android_device_001',
      'X-User-Id': '101'
    }
  });
  return response.data;
};
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasUpdate": true,
    "latestVersion": "1.0.2",
    "currentVersion": "1.0.0",
    "configSource": "USER_SPECIFIC",
    "assignTime": "2025-01-21T10:00:00"
  }
}
```

---

## 📱 前端集成建议

### 1. 封装 API 服务类

```javascript
class LogControlService {
  constructor(baseURL, authToken) {
    this.baseURL = baseURL;
    this.authToken = authToken;
  }

  // 获取日志配置
  async getLogConfig(deviceId, userId, appVersion) {
    const headers = {};
    if (deviceId) headers['X-Device-Id'] = deviceId;
    if (userId) headers['X-User-Id'] = userId;
    if (appVersion) headers['X-App-Version'] = appVersion;

    return await this.request('GET', '/config/get', null, headers);
  }

  // 上传设备信息
  async uploadDeviceInfo(deviceInfo) {
    return await this.request('POST', '/device/upload', deviceInfo, {
      'Content-Type': 'application/json',
      'x-auth-token': this.authToken
    });
  }

  // 通用请求方法
  async request(method, endpoint, data = null, headers = {}) {
    const url = `${this.baseURL}/logcontrol${endpoint}`;
    const config = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      return await response.json();
    } catch (error) {
      console.error(`API请求失败 ${method} ${endpoint}:`, error);
      throw error;
    }
  }
}

// 使用示例
const logControlService = new LogControlService('http://localhost:8080', 'your_token');
```

### 2. 错误处理

```javascript
const handleApiResponse = (response) => {
  if (response.code === 200) {
    return response.data;
  } else {
    throw new Error(response.message || '请求失败');
  }
};

// 使用示例
try {
  const config = await logControlService.getLogConfig('device123', '101', '1.0.0');
  const data = handleApiResponse(config);
  console.log('配置获取成功:', data);
} catch (error) {
  console.error('配置获取失败:', error.message);
}
```

### 3. 本地缓存策略

```javascript
class ConfigCache {
  static CACHE_KEY = 'logcontrol_config';
  static CACHE_DURATION = 5 * 60 * 1000; // 5分钟

  static set(config) {
    const cacheData = {
      config,
      timestamp: Date.now()
    };
    localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheData));
  }

  static get() {
    const cached = localStorage.getItem(this.CACHE_KEY);
    if (!cached) return null;

    const { config, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp > this.CACHE_DURATION) {
      localStorage.removeItem(this.CACHE_KEY);
      return null;
    }

    return config;
  }

  static clear() {
    localStorage.removeItem(this.CACHE_KEY);
  }
}
```

---

## ⚠️ 注意事项

1. **认证差异**：
   - `/config/get` 接口无需认证，可直接访问
   - `/device/upload` 接口需要 `x-auth-token` 认证

2. **参数类型**：
   - `userId` 使用 String 类型，避免大整数精度问题
   - 所有请求头参数都是可选的

3. **错误处理**：
   - 接口有完善的降级机制，失败时会返回默认配置
   - 建议实现本地缓存和重试机制

4. **限流保护**：
   - 接口有限流保护，避免频繁请求
   - 建议合理控制请求频率

5. **配置优先级**：
   - 用户配置 > 设备配置 > 默认配置
   - 传递用户ID可获得个性化配置

---

## 🚀 快速开始

```javascript
// 1. 初始化服务
const logControl = new LogControlService('http://localhost:8080', 'your_token');

// 2. 获取配置
const config = await logControl.getLogConfig('device123', '101', '1.0.0');

// 3. 上传设备信息
const deviceInfo = {
  deviceId: 'device123',
  userId: '101',
  brand: 'Huawei',
  model: 'Mate 40 Pro'
};
await logControl.uploadDeviceInfo(deviceInfo);
```

---

*文档更新时间：2025-01-21*
