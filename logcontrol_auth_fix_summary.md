# LogControl 接口认证修复总结

## 🎯 修复目标

根据后端API文档 `logcontrol-api-frontend-guide.md`，对两个logcontrol接口进行差异化认证处理：

1. `/api/logcontrol/config/get` - **无需认证**（公开接口）
2. `/api/logcontrol/device/upload` - **需要认证**，保持 `X-Auth-Token` 格式

## 🔧 修复内容

### 1. 新增认证判断方法

在 `TokenInterceptor.kt` 中新增 `needsAuthentication()` 方法：

```kotlin
/**
 * 判断接口是否需要认证
 * 根据后端API文档，部分logcontrol接口为公开接口
 */
private fun needsAuthentication(url: String): Boolean {
    // 公开接口列表（无需认证）
    val publicApiPaths = listOf(
        "/api/logcontrol/config/get",           // 获取日志配置（公开接口）
        "/api/wechat/staff/app/login",          // 登录接口
        "/api/wechat/staff/app/captcha",        // 验证码接口
        "/api/wechat/staff/app/key"             // 密钥接口
    )
    
    // 检查是否为公开接口
    for (publicPath in publicApiPaths) {
        if (url.contains(publicPath)) {
            return false
        }
    }
    
    // 其他接口都需要认证
    return true
}
```

### 2. 修改认证头添加逻辑

```kotlin
// 检查是否需要添加认证头
val needsAuth = needsAuthentication(original.url.toString())

// 使用X-Auth-Token而不是Authorization，与UNI-APP保持一致
if (needsAuth && !token.isNullOrEmpty()) {
    requestBuilder.header("X-Auth-Token", token)
    Log.d(TAG, "添加令牌头 X-Auth-Token: ${token.take(10)}...")
    Log.d(TAG, "Token已添加到请求头，等待服务器响应验证")
} else if (needsAuth && token.isNullOrEmpty()) {
    Log.w(TAG, "请求需要认证但令牌为空，可能导致认证失败")
} else if (!needsAuth) {
    Log.d(TAG, "公开接口，跳过认证头添加: ${original.url}")
}
```

### 3. 修改401/403状态码处理

```kotlin
// 检查响应状态码，处理401未授权错误（令牌过期）- 仅对需要认证的接口
if ((response.code == 401 || response.code == 403) && needsAuthentication(original.url.toString())) {
    // 执行token刷新逻辑
} else if ((response.code == 401 || response.code == 403) && !needsAuthentication(original.url.toString())) {
    Log.d(TAG, "公开接口返回401/403，可能是服务器配置问题: ${original.url}")
}
```

### 4. 修改Token过期处理

```kotlin
// 在OkHttp层面直接处理Token过期（仅对需要认证的接口）
if (needsAuthentication(original.url.toString())) {
    val responseBody = response.peekBody(4096).string()
    if (responseBody.contains("\"code\":401") &&
        (responseBody.contains("\"message\":\"会话过期\"") ||
         responseBody.contains("\"message\":\"您的帐号已从其他客户端登录\""))) {
        handleTokenExpiredInOkHttp(original.url.toString(), responseBody)
    }
}
```

### 5. 更新核心API判断逻辑

```kotlin
private fun isCoreApiRequest(url: String): Boolean {
    val coreApiPaths = listOf(
        "/api/wechat/staff/",           // 员工相关API
        "/api/magina/system/",          // 系统资源API
        "/api/wechat/workorder/",       // 工单相关API
        "/api/wechat/customer/",        // 客户相关API
        "/api/wechat/warehouse/",       // 仓库相关API
        "/api/wechat/engineer/",        // 工程师相关API
        "/api/engineer/",               // 工程师API（新格式）
        "/api/cos/",                    // COS相关API
        "/api/logcontrol/device/upload" // 设备信息上传（需要Token验证）
    )

    // 排除不需要Token的API
    val excludeApiPaths = listOf(
        "/api/magina/anno/",            // 匿名API
        "/api/logcontrol/config/get"    // 日志配置获取（公开接口）
    )
}
```

## 🎯 预期效果

修复后的行为：

1. **`/api/logcontrol/config/get`**：
   - ✅ 不添加 `X-Auth-Token` 头
   - ✅ 返回401时不触发token刷新
   - ✅ 不触发会话过期处理

2. **`/api/logcontrol/device/upload`**：
   - ✅ 正常添加 `X-Auth-Token` 头
   - ✅ 返回401时正常触发token刷新
   - ✅ 正常触发会话过期处理

3. **其他接口**：
   - ✅ 保持原有认证逻辑不变

## 📝 测试建议

1. **清除应用数据**，重新登录
2. **观察日志**，确认：
   - 配置获取接口不添加认证头
   - 设备上传接口正常添加认证头
   - 不再出现配置接口的401会话过期错误

## ✅ 修复完成

此修复解决了logcontrol接口认证不一致的问题，确保：
- 公开接口不进行认证
- 需要认证的接口正常认证
- 保持 `X-Auth-Token` 头格式不变
