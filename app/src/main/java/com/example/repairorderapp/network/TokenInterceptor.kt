package com.example.repairorderapp.network

import android.content.Context
import android.content.Intent
import android.util.Log
import com.example.repairorderapp.RepairOrderApp
import com.example.repairorderapp.ui.login.LoginActivity
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okio
import retrofit2.Call
import retrofit2.Callback
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.locks.ReentrantLock

/**
 * 令牌拦截器
 * 负责为请求添加认证令牌，处理令牌刷新和会话过期逻辑
 */
class TokenInterceptor : Interceptor {
    private val TAG = "TokenInterceptor"

    // 添加锁，防止多个请求同时刷新token
    private val refreshTokenLock = ReentrantLock()

    // 标记token是否正在刷新
    private var isRefreshing = false

    // 刷新失败计数
    private var refreshFailCount = 0

    companion object {
        // Token过期对话框状态管理
        @Volatile
        private var isTokenExpiredDialogShowing = false
        private var lastTokenExpiredTime = 0L
        private var tokenExpiredCount = 0 // Token过期次数计数
        private const val TOKEN_EXPIRED_COOLDOWN = 10000L // 10秒冷却时间
        private const val MAX_TOKEN_EXPIRED_DIALOGS = 3 // 最多显示3次对话框

        // 登录保护机制
        @Volatile
        private var lastLoginTime = 0L
        private const val LOGIN_PROTECTION_PERIOD = 30000L // 登录后30秒保护期

        // 重置Token过期状态（用于新的登录会话）
        fun resetTokenExpiredState() {
            isTokenExpiredDialogShowing = false
            lastTokenExpiredTime = 0L
            tokenExpiredCount = 0
            lastLoginTime = System.currentTimeMillis() // 记录登录时间
            android.util.Log.d("TokenInterceptor", "Token过期状态已重置，开始登录保护期")
        }
    }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        try {
            val original = chain.request()

            // 获取保存的Token - 使用accessToken键名与UNI-APP保持一致
            val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
            var token = tokenPrefs.getString("accessToken", "")
            val userId = tokenPrefs.getString("userId", "")
            val refreshToken = tokenPrefs.getString("refreshToken", "")

            // 记录请求信息和详细的Token状态
            Log.d(TAG, "拦截请求: ${original.url}")
            Log.d(TAG, "当前线程: ${Thread.currentThread().name}")
            Log.d(TAG, "Token读取详情: accessToken=${if (token.isNullOrEmpty()) "空" else "长度=${token.length}"}, userId=$userId")

            // 检查令牌是否为空，如果为空直接报错，不再尝试从其他位置恢复
            if (token.isNullOrEmpty()) {
                Log.e(TAG, "令牌为空，所有存储位置都为空")

                // 尝试从SharedPreferences中读取所有相关键值，用于调试
                val allKeys = tokenPrefs.all
                Log.e(TAG, "token_pref中的所有键值: $allKeys")
            }

            Log.d(TAG, "令牌状态: ${if (token.isNullOrEmpty()) "空" else "长度=${token.length}"}")

            // 构建新的请求，添加header
            val requestBuilder = original.newBuilder()
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")

            // 使用X-Auth-Token而不是Authorization，与UNI-APP保持一致
            if (!token.isNullOrEmpty()) {
                requestBuilder.header("X-Auth-Token", token)
                Log.d(TAG, "添加令牌头 X-Auth-Token: ${token.take(10)}...")

                // 移除主动刷新逻辑，只依赖服务器401/403响应进行被动刷新
                // 这样可以避免因本地时间戳不准确导致的过早刷新问题
                Log.d(TAG, "Token已添加到请求头，等待服务器响应验证")
            } else {
                Log.w(TAG, "请求未添加令牌，可能导致认证失败")
                
                // 检查是否是登录相关请求，如果不是，可能需要重新登录
                if (!original.url.toString().contains("/login") && 
                    !original.url.toString().contains("/captcha") && 
                    !original.url.toString().contains("/key")) {
                    Log.e(TAG, "非登录请求但令牌为空，可能需要重新登录")
                }
            }

            // 如果有用户ID，添加X-CUSTOMER-ID头
            if (!userId.isNullOrEmpty()) {
                requestBuilder.header("X-CUSTOMER-ID", userId)
                Log.d(TAG, "添加用户ID头 X-CUSTOMER-ID: $userId")
            }

            val request = requestBuilder.build()

            // 详细日志输出LogControl接口的请求信息
            val isLogControlApi = original.url.toString().contains("/api/logcontrol/")
            if (isLogControlApi) {
                logLogControlRequestDetails(request, original.url.toString())
            }

            // 尝试打印所有请求头（调试用）
            Log.d(TAG, "请求头信息:")
            request.headers.forEach {
                val isAuthToken = it.first.equals("X-Auth-Token", true) || it.first.equals("x-auth-token", true)
                Log.d(TAG, "  ${it.first}: ${if (isAuthToken) "******" else it.second}")
            }

            // 处理响应
            var response = chain.proceed(request)

            // 检查响应状态码，处理401未授权错误（令牌过期）
            if (response.code == 401 || response.code == 403) {
                Log.e(TAG, "会话已过期(${response.code})，尝试刷新令牌")

                // 尝试刷新token
                response.close()

                // 获取refreshToken
                if (refreshToken.isNullOrEmpty()) {
                    // 没有refreshToken，直接跳转登录页面
                    Log.e(TAG, "没有刷新令牌，需要重新登录")
                    // 在启动页面时不直接处理会话过期，由SplashActivity统一处理
                    if (!RepairOrderApp.isInSplashActivity) {
                        TokenManager.getInstance(RepairOrderApp.instance).handleSessionExpired()
                    }
                    throw IOException("会话已过期，请重新登录")
                }

                // 有refreshToken，尝试刷新token
                val newTokens = refreshToken(refreshToken, request)
                if (newTokens != null) {
                    // 刷新成功，使用新token重试请求
                    val newRequest = request.newBuilder()
                        .header("X-Auth-Token", newTokens.first)
                        .build()

                    return chain.proceed(newRequest)
                } else {
                    // 刷新失败，跳转登录页面
                    // 在启动页面时不直接处理会话过期，由SplashActivity统一处理
                    if (!RepairOrderApp.isInSplashActivity) {
                        TokenManager.getInstance(RepairOrderApp.instance).handleSessionExpired()
                    }
                    throw IOException("刷新令牌失败，请重新登录")
                }
            }

            // 在OkHttp层面直接处理Token过期
            val responseBody = response.peekBody(4096).string()
            if (responseBody.contains("\"code\":401") &&
                (responseBody.contains("\"message\":\"会话过期\"") ||
                 responseBody.contains("\"message\":\"您的帐号已从其他客户端登录\""))) {

                Log.w(TAG, "检测到Token过期: ${original.url}")

                // 在OkHttp层面直接处理Token过期
                handleTokenExpiredInOkHttp(original.url.toString(), responseBody)
            }

            return response

        } catch (e: Exception) {
            Log.e(TAG, "Network error: ${e.message}")

            // 处理不同类型的网络错误
            val errorMessage = when (e) {
                is SocketTimeoutException -> "请求超时，请检查网络连接"
                is UnknownHostException -> "无法连接到服务器，请检查网络连接"
                is ConnectException -> "连接服务器失败，请检查网络设置"
                else -> "网络错误: ${e.message}"
            }

            throw IOException(errorMessage, e)
        }
    }

    /**
     * 判断是否为核心API请求
     * 只有核心API的401错误才会触发会话过期处理
     */
    private fun isCoreApiRequest(url: String): Boolean {
        val coreApiPaths = listOf(
            "/api/wechat/staff/",           // 员工相关API
            "/api/magina/system/",          // 系统资源API
            "/api/wechat/workorder/",       // 工单相关API
            "/api/wechat/customer/",        // 客户相关API
            "/api/wechat/warehouse/",       // 仓库相关API
            "/api/wechat/engineer/",        // 工程师相关API
            "/api/engineer/",               // 工程师API（新格式）
            "/api/cos/",                    // COS相关API
            "/api/logcontrol/"              // 日志控制API（需要Token验证）
        )

        // 排除不需要Token的API
        val excludeApiPaths = listOf(
            "/api/magina/anno/",            // 匿名API
            // 移除 "/api/logcontrol/" - 这些接口实际上需要Token验证
            // 移除 "/device/upload" 和 "/config/get" - 这些是logcontrol的子路径，也需要Token
        )

        // 如果是排除的API，不认为是核心API
        for (excludePath in excludeApiPaths) {
            if (url.contains(excludePath)) {
                return false
            }
        }

        // 如果是核心API，返回true
        for (corePath in coreApiPaths) {
            if (url.contains(corePath)) {
                return true
            }
        }

        return false
    }

    /**
     * 在OkHttp层面直接处理Token过期
     */
    private fun handleTokenExpiredInOkHttp(url: String, responseBody: String) {
        try {
            Log.w(TAG, "在OkHttp层面处理Token过期: $url")

            val currentTime = System.currentTimeMillis()

            // 检查登录保护期
            if (currentTime - lastLoginTime < LOGIN_PROTECTION_PERIOD) {
                val remainingProtection = (LOGIN_PROTECTION_PERIOD - (currentTime - lastLoginTime)) / 1000
                Log.w(TAG, "登录保护期内，跳过Token过期处理: $url (剩余保护时间: ${remainingProtection}秒)")
                return
            }

            // 检查冷却时间，避免频繁弹窗
            if (currentTime - lastTokenExpiredTime < TOKEN_EXPIRED_COOLDOWN) {
                Log.d(TAG, "Token过期处理在冷却期内，跳过: $url (剩余冷却时间: ${(TOKEN_EXPIRED_COOLDOWN - (currentTime - lastTokenExpiredTime)) / 1000}秒)")
                return
            }

            // 检查是否已经有对话框显示
            if (isTokenExpiredDialogShowing) {
                Log.d(TAG, "Token过期对话框已显示，跳过: $url")
                return
            }

            // 增加Token过期次数计数
            tokenExpiredCount++
            Log.i(TAG, "Token过期次数: $tokenExpiredCount/$MAX_TOKEN_EXPIRED_DIALOGS")

            // 检查是否达到最大显示次数
            if (tokenExpiredCount >= MAX_TOKEN_EXPIRED_DIALOGS) {
                Log.w(TAG, "第三次Token过期，直接跳转登录页面: $url")
                lastTokenExpiredTime = currentTime

                // 使用Handler确保在主线程中执行
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    directJumpToLogin("第三次Token过期")
                }
                return
            }

            // 设置对话框显示状态和时间
            isTokenExpiredDialogShowing = true
            lastTokenExpiredTime = currentTime

            // 使用Handler确保在主线程中执行UI操作
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                showTokenExpiredDialog(url)
            }

        } catch (e: Exception) {
            Log.e(TAG, "在OkHttp层面处理Token过期失败", e)
            isTokenExpiredDialogShowing = false
        }
    }

    /**
     * 显示Token过期对话框
     */
    private fun showTokenExpiredDialog(url: String) {
        try {
            // 获取当前Activity
            val currentActivity = getCurrentActivity()
            if (currentActivity == null) {
                Log.w(TAG, "无法获取当前Activity，重置对话框状态")
                isTokenExpiredDialogShowing = false
                return
            }

            // 检查Activity状态
            if (currentActivity.isFinishing || currentActivity.isDestroyed) {
                Log.w(TAG, "Activity已销毁，重置对话框状态")
                isTokenExpiredDialogShowing = false
                return
            }

            Log.i(TAG, "准备显示Token过期对话框 (第${tokenExpiredCount}次)，当前Activity: ${currentActivity.javaClass.simpleName}")

            // 显示Token过期对话框
            val dialog = androidx.appcompat.app.AlertDialog.Builder(currentActivity)
                .setTitle("会话过期提醒")
                .setMessage("您的登录会话已过期，是否立即重新登录？")
                .setPositiveButton("立即登录") { _, _ ->
                    Log.i(TAG, "用户选择立即重新登录 (第${tokenExpiredCount}次)")
                    isTokenExpiredDialogShowing = false
                    clearTokenAndGoToLogin(currentActivity)
                }
                .setNegativeButton("稍后再说") { _, _ ->
                    Log.i(TAG, "用户选择稍后处理Token过期 (第${tokenExpiredCount}次)")
                    isTokenExpiredDialogShowing = false
                    // 用户选择稍后再说，不跳转登录页面，保持当前页面
                }
                .setOnDismissListener {
                    Log.d(TAG, "Token过期对话框被关闭")
                    isTokenExpiredDialogShowing = false
                }
                .setCancelable(false)
                .create()

            // 尝试显示对话框
            dialog.show()
            Log.i(TAG, "Token过期对话框显示成功 (第${tokenExpiredCount}次)")

        } catch (e: Exception) {
            Log.e(TAG, "显示Token过期对话框失败 (第${tokenExpiredCount}次): ${e.message}", e)
            isTokenExpiredDialogShowing = false

            // 如果对话框显示失败，记录错误但不强制跳转
            Log.w(TAG, "对话框显示失败，重置状态等待下次处理")
        }
    }

    /**
     * 直接跳转到登录页面（第三次Token过期时使用）
     */
    private fun directJumpToLogin(reason: String) {
        try {
            val currentActivity = getCurrentActivity()
            if (currentActivity == null) {
                Log.w(TAG, "无法获取当前Activity，无法跳转登录页面: $reason")
                return
            }

            if (currentActivity.isFinishing || currentActivity.isDestroyed) {
                Log.w(TAG, "Activity已销毁，无法跳转登录页面: $reason")
                return
            }

            Log.w(TAG, "$reason，直接跳转登录页面，当前Activity: ${currentActivity.javaClass.simpleName}")
            clearTokenAndGoToLogin(currentActivity)

        } catch (e: Exception) {
            Log.e(TAG, "直接跳转登录页面失败: $reason", e)
        }
    }

    /**
     * 获取当前Activity
     */
    private fun getCurrentActivity(): android.app.Activity? {
        return try {
            val activity = RepairOrderApp.instance.getCurrentActivity()
            Log.d(TAG, "获取当前Activity: ${activity?.javaClass?.simpleName ?: "null"}")
            activity
        } catch (e: Exception) {
            Log.e(TAG, "获取当前Activity失败", e)
            null
        }
    }



    /**
     * 清除Token并跳转到登录页面
     */
    private fun clearTokenAndGoToLogin(activity: android.app.Activity) {
        try {
            Log.i(TAG, "开始清除Token并跳转登录页面")

            // 重置Token过期状态
            resetTokenExpiredState()

            // 清除Token相关数据
            val tokenPrefs = activity.getSharedPreferences("token_pref", android.content.Context.MODE_PRIVATE)
            tokenPrefs.edit().clear().apply()

            // 清除登录状态
            val sharedPrefsManager = com.example.repairorderapp.util.SharedPrefsManager(activity)
            sharedPrefsManager.saveLoginStatus(false)
            sharedPrefsManager.clearUserData()

            // 跳转到登录页面
            val intent = android.content.Intent(activity, com.example.repairorderapp.ui.login.LoginActivity::class.java)
            intent.flags = android.content.Intent.FLAG_ACTIVITY_NEW_TASK or android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK
            activity.startActivity(intent)
            activity.finish()

            Log.i(TAG, "Token清除完成，已跳转到登录页面")

        } catch (e: Exception) {
            Log.e(TAG, "清除Token并跳转登录失败", e)
        }
    }

    // 移除主动刷新token功能，只保留被动刷新机制
    // 这样可以避免因本地时间戳不准确导致的过早刷新问题

    /**
     * 刷新token
     * @return Pair<String, String>? 第一个元素是新的accessToken，第二个元素是新的refreshToken，null表示刷新失败
     */
    private fun refreshToken(refreshToken: String, request: Request): Pair<String, String>? {
        try {
            refreshTokenLock.lock()
            if (isRefreshing) {
                Thread.sleep(6000)
                val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                val newToken = tokenPrefs.getString("accessToken", "")
                val newRefreshToken = tokenPrefs.getString("refreshToken", "")
                if (!newToken.isNullOrEmpty() && !newRefreshToken.isNullOrEmpty()) {
                    return Pair(newToken, newRefreshToken)
                }
                return null
            }
            isRefreshing = true
            val loginService = ApiClient.createService(com.example.repairorderapp.network.service.LoginService::class.java)
            val params = mapOf("refreshToken" to refreshToken)
            val response = loginService.refreshToken(params).execute()
            if (response.isSuccessful) {
                try {
                    val responseStr = response.body()?.string() ?: ""
                    val jsonObject = org.json.JSONObject(responseStr)
                    val code = jsonObject.optInt("code", -1)
                    if (code == 200) {
                        val data = jsonObject.optJSONObject("data")
                        if (data != null) {
                            val newToken = data.optString("token", "")
                            val newRefreshToken = data.optString("refreshToken", "")
                            if (newToken.isNotEmpty() && newRefreshToken.isNotEmpty()) {
                                val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                                tokenPrefs.edit().apply {
                                    putString("accessToken", newToken)
                                    putString("refreshToken", newRefreshToken)
                                    apply()
                                }
                                refreshFailCount = 0 // 刷新成功，计数归零
                                Log.d(TAG, "成功刷新令牌")
                                return Pair(newToken, newRefreshToken)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析刷新token响应失败", e)
                }
            }
            Log.e(TAG, "刷新令牌失败: ${response.code()}")
            refreshFailCount++
            if (refreshFailCount < 3) {
                // 允许继续用旧token，后台重试
                val tokenPrefs = RepairOrderApp.instance.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
                val token = tokenPrefs.getString("accessToken", "") ?: ""
                return Pair(token, refreshToken)
            } else {
                // 超过3次才真正弹窗，在启动页面时不直接处理会话过期，由SplashActivity统一处理
                if (!RepairOrderApp.isInSplashActivity) {
                    TokenManager.getInstance(RepairOrderApp.instance).handleSessionExpired()
                }
                refreshFailCount = 0
                throw IOException("刷新令牌失败，请重新登录")
            }
        } catch (e: Exception) {
            Log.e(TAG, "刷新令牌异常: ${e.message}")
            return null
        } finally {
            isRefreshing = false
            refreshTokenLock.unlock()
        }
    }

    /**
     * 详细记录LogControl接口的请求信息
     */
    private fun logLogControlRequestDetails(request: Request, url: String) {
        try {
            Log.i(TAG, "========== LogControl接口详细信息 ==========")
            Log.i(TAG, "URL: $url")
            Log.i(TAG, "Method: ${request.method}")

            // 输出所有请求头（包括认证头的完整信息）
            Log.i(TAG, "请求头详情:")
            request.headers.forEach { header ->
                when {
                    header.first.equals("x-auth-token", true) -> {
                        Log.i(TAG, "  ${header.first}: ${header.second}")
                    }
                    header.first.equals("X-Auth-Token", true) -> {
                        Log.i(TAG, "  ${header.first}: ${header.second}")
                    }
                    else -> {
                        Log.i(TAG, "  ${header.first}: ${header.second}")
                    }
                }
            }

            // 输出请求体（如果有）
            val requestBody = request.body
            if (requestBody != null) {
                Log.i(TAG, "请求体信息:")
                Log.i(TAG, "  Content-Type: ${requestBody.contentType()}")
                Log.i(TAG, "  Content-Length: ${requestBody.contentLength()}")

                // 尝试读取请求体内容（仅对小于1KB的内容）
                if (requestBody.contentLength() < 1024) {
                    try {
                        val buffer = okio.Buffer()
                        requestBody.writeTo(buffer)
                        val bodyContent = buffer.readUtf8()
                        Log.i(TAG, "  请求体内容: $bodyContent")
                    } catch (e: Exception) {
                        Log.w(TAG, "  无法读取请求体内容: ${e.message}")
                    }
                } else {
                    Log.i(TAG, "  请求体过大，跳过内容输出")
                }
            } else {
                Log.i(TAG, "无请求体")
            }

            // 输出URL参数
            val httpUrl = request.url
            if (httpUrl.querySize > 0) {
                Log.i(TAG, "URL参数:")
                for (i in 0 until httpUrl.querySize) {
                    Log.i(TAG, "  ${httpUrl.queryParameterName(i)}: ${httpUrl.queryParameterValue(i)}")
                }
            } else {
                Log.i(TAG, "无URL参数")
            }

            Log.i(TAG, "==========================================")
        } catch (e: Exception) {
            Log.e(TAG, "记录LogControl请求详情时出错: ${e.message}")
        }
    }
}